import { NextRequest, NextResponse } from 'next/server'
import { OrderIntakeAgent } from '@/agents/order-intake-agent'
import { InvoiceAgent } from '@/agents/invoice-agent'
import { AgentContext } from '@/types'
import { prisma } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const requiredFields = ['customerName', 'customerPhone', 'customerAddress', 'city', 'quantity']
    const missingFields = requiredFields.filter(field => !body[field])
    
    if (missingFields.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      }, { status: 400 })
    }

    // Create initial context (orderId will be set by the agent)
    const context: AgentContext = {
      orderId: '', // Will be set after order creation
      metadata: {
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
        timestamp: new Date().toISOString(),
      }
    }

    // Step 1: Process order intake
    const orderAgent = new OrderIntakeAgent(context)
    const orderResult = await orderAgent.execute(body)

    if (!orderResult.success) {
      return NextResponse.json({
        success: false,
        error: orderResult.error
      }, { status: 400 })
    }

    // Update context with the created order ID
    context.orderId = orderResult.data.orderId

    // Step 2: Generate invoice
    const invoiceAgent = new InvoiceAgent(context)
    const invoiceResult = await invoiceAgent.execute({
      orderId: orderResult.data.orderId,
      generatePDF: true
    })

    if (!invoiceResult.success) {
      console.error('Invoice generation failed:', invoiceResult.error)
      // Continue without failing the order - invoice can be generated later
    }

    return NextResponse.json({
      success: true,
      data: {
        orderId: orderResult.data.orderId,
        orderNumber: orderResult.data.orderNumber,
        pricing: orderResult.data.pricing,
        invoice: invoiceResult.success ? invoiceResult.data.invoice : null,
        nextSteps: [
          'Payment verification will be processed automatically',
          'You will receive tracking updates via SMS/Email',
          'Delivery will be scheduled after payment confirmation'
        ]
      }
    })

  } catch (error) {
    console.error('Order creation error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error. Please try again.'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (status) {
      where.status = status
    }
    
    if (search) {
      where.OR = [
        { orderNumber: { contains: search, mode: 'insensitive' } },
        { customerName: { contains: search, mode: 'insensitive' } },
        { customerPhone: { contains: search } },
        { customerEmail: { contains: search, mode: 'insensitive' } },
      ]
    }

    // Get orders with pagination
    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          invoice: true,
          payment: true,
          delivery: true,
          agentLogs: {
            orderBy: { createdAt: 'desc' },
            take: 5,
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.order.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        }
      }
    })

  } catch (error) {
    console.error('Get orders error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch orders'
    }, { status: 500 })
  }
}
