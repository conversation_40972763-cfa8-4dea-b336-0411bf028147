import { NextRequest, NextResponse } from 'next/server'
import { seedDemoData, clearDemoData } from '@/lib/seed-demo-data'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    if (action === 'seed') {
      const result = await seedDemoData()
      return NextResponse.json({
        success: true,
        message: 'Demo data seeded successfully',
        data: result
      })
    } else if (action === 'clear') {
      const result = await clearDemoData()
      return NextResponse.json({
        success: true,
        message: 'Demo data cleared successfully',
        data: result
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action. Use "seed" or "clear"'
      }, { status: 400 })
    }

  } catch (error) {
    console.error('Demo data operation error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to perform demo data operation'
    }, { status: 500 })
  }
}
