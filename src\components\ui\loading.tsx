import * as React from "react"
import { cn } from "@/lib/utils"

export interface LoadingProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'dots' | 'pulse'
}

const Loading = React.forwardRef<HTMLDivElement, LoadingProps>(
  ({ className, size = 'md', variant = 'spinner', ...props }, ref) => {
    const sizes = {
      sm: "w-4 h-4",
      md: "w-6 h-6", 
      lg: "w-8 h-8"
    }

    if (variant === 'spinner') {
      return (
        <div
          ref={ref}
          className={cn(
            "border-2 border-gray-200 border-t-leaf-500 rounded-full animate-spin",
            sizes[size],
            className
          )}
          {...props}
        />
      )
    }

    if (variant === 'dots') {
      return (
        <div
          ref={ref}
          className={cn("flex space-x-1", className)}
          {...props}
        >
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                "bg-leaf-500 rounded-full animate-pulse",
                size === 'sm' && "w-1 h-1",
                size === 'md' && "w-2 h-2",
                size === 'lg' && "w-3 h-3"
              )}
              style={{
                animationDelay: `${i * 0.2}s`,
                animationDuration: '1s'
              }}
            />
          ))}
        </div>
      )
    }

    if (variant === 'pulse') {
      return (
        <div
          ref={ref}
          className={cn(
            "bg-leaf-500 rounded-full animate-pulse",
            sizes[size],
            className
          )}
          {...props}
        />
      )
    }

    return null
  }
)
Loading.displayName = "Loading"

// Loading overlay component
export interface LoadingOverlayProps {
  isLoading: boolean
  message?: string
  children: React.ReactNode
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  message = "Loading...",
  children
}) => {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
          <div className="text-center">
            <Loading size="lg" className="mx-auto mb-4" />
            <p className="text-gray-600 font-medium">{message}</p>
          </div>
        </div>
      )}
    </div>
  )
}

export { Loading }
