{"name": "mangoease-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate"}, "dependencies": {"@prisma/client": "^5.7.1", "@supabase/supabase-js": "^2.38.5", "next": "14.0.4", "react": "^18", "react-dom": "^18", "typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "langchain": "^0.0.212", "openai": "^4.20.1", "tesseract.js": "^5.0.4", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "axios": "^1.6.2", "zod": "^3.22.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "lucide-react": "^0.294.0", "framer-motion": "^10.16.16", "next-auth": "^4.24.5", "bcryptjs": "^2.4.3", "@types/bcryptjs": "^2.4.6", "jsonwebtoken": "^9.0.2", "@types/jsonwebtoken": "^9.0.5", "nodemailer": "^6.9.7", "@types/nodemailer": "^6.4.14", "multer": "^1.4.5-lts.1", "@types/multer": "^1.4.11", "sharp": "^0.33.1", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-pdf": "^7.6.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"prisma": "^5.7.1", "eslint": "^8", "eslint-config-next": "14.0.4"}}