import { BaseAgent } from './base-agent'
import { <PERSON><PERSON><PERSON>, AgentContext, AgentResult, OrderFormData } from '@/types'
import { prisma } from '@/lib/db'
import { calculateOrderTotal } from '@/lib/config'
import { generateOrderNumber } from '@/lib/utils'
import { OrderStatus, PaymentStatus } from '@prisma/client'

interface OrderIntakeInput {
  customerName: string
  customerPhone: string
  customerEmail?: string
  customerAddress: string
  city: string
  quantity: number
  userMessage?: string
}

export class OrderIntakeAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super(AgentType.ORDER_INTAKE, context)
  }

  async execute(input: OrderIntakeInput): Promise<AgentResult> {
    try {
      await this.logAction('order_intake_started', input, null, true)

      // Validate input
      const requiredFields = ['customerName', 'customerPhone', 'customerAddress', 'city', 'quantity']
      if (!this.validateInput(input, requiredFields)) {
        const error = 'Missing required fields for order creation'
        await this.logAction('validation_failed', input, null, false, error)
        return this.handleError(new Error(error), 'validation')
      }

      // Sanitize input
      const sanitizedInput = this.sanitizeInput(input) as OrderIntakeInput

      // Validate quantity
      if (sanitizedInput.quantity <= 0 || sanitizedInput.quantity > 100) {
        const error = 'Invalid quantity. Must be between 0.1 and 100 kg'
        await this.logAction('quantity_validation_failed', input, null, false, error)
        return this.handleError(new Error(error), 'quantity_validation')
      }

      // Calculate pricing
      const pricing = calculateOrderTotal(sanitizedInput.quantity, sanitizedInput.city)
      
      // Generate order number
      const orderNumber = generateOrderNumber()

      // Create order in database
      const order = await prisma.order.create({
        data: {
          orderNumber,
          customerName: sanitizedInput.customerName,
          customerPhone: this.formatPhoneNumber(sanitizedInput.customerPhone),
          customerEmail: sanitizedInput.customerEmail || null,
          customerAddress: sanitizedInput.customerAddress,
          city: sanitizedInput.city,
          quantity: sanitizedInput.quantity,
          pricePerKg: pricing.subtotal / sanitizedInput.quantity,
          deliveryCharge: pricing.deliveryCharge,
          totalAmount: pricing.total,
          status: OrderStatus.PENDING,
          paymentStatus: PaymentStatus.PENDING,
        },
      })

      // Update context with the new order ID
      this.context.orderId = order.id

      const result = {
        orderId: order.id,
        orderNumber: order.orderNumber,
        pricing,
        order,
      }

      await this.logAction('order_created', sanitizedInput, result, true)

      return {
        success: true,
        data: result,
        nextAction: 'generate_invoice',
      }
    } catch (error) {
      await this.logAction('order_creation_failed', input, null, false, error instanceof Error ? error.message : 'Unknown error')
      return this.handleError(error, 'order_creation')
    }
  }

  async processNaturalLanguageOrder(userMessage: string): Promise<AgentResult> {
    try {
      await this.logAction('nlp_processing_started', { userMessage }, null, true)

      // Simple NLP processing for mango orders
      const extractedData = this.extractOrderData(userMessage)
      
      if (!extractedData.quantity) {
        return {
          success: false,
          error: 'Could not understand the quantity. Please specify how many kg of mangoes you want.',
        }
      }

      await this.logAction('nlp_extraction_completed', { userMessage }, extractedData, true)

      return {
        success: true,
        data: extractedData,
        nextAction: 'collect_customer_details',
      }
    } catch (error) {
      await this.logAction('nlp_processing_failed', { userMessage }, null, false, error instanceof Error ? error.message : 'Unknown error')
      return this.handleError(error, 'nlp_processing')
    }
  }

  private extractOrderData(message: string): Partial<OrderIntakeInput> {
    const lowerMessage = message.toLowerCase()
    
    // Extract quantity
    let quantity = 0
    
    // Look for patterns like "5kg", "5 kg", "5 kilos", "5 kilo", "five kg"
    const quantityPatterns = [
      /(\d+(?:\.\d+)?)\s*(?:kg|kilo|kilos|kilogram|kilograms)/i,
      /(\d+(?:\.\d+)?)\s*(?:mango|mangoes)/i,
      /(one|two|three|four|five|six|seven|eight|nine|ten)\s*(?:kg|kilo|kilos)/i,
    ]

    for (const pattern of quantityPatterns) {
      const match = message.match(pattern)
      if (match) {
        if (match[1]) {
          quantity = parseFloat(match[1])
        } else {
          // Convert word numbers to digits
          const wordToNumber: { [key: string]: number } = {
            'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
            'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10
          }
          quantity = wordToNumber[match[1]] || 0
        }
        break
      }
    }

    // Extract city if mentioned
    let city = ''
    const pakistaniCities = [
      'karachi', 'lahore', 'islamabad', 'rawalpindi', 'faisalabad',
      'multan', 'peshawar', 'quetta', 'sialkot', 'gujranwala'
    ]
    
    for (const cityName of pakistaniCities) {
      if (lowerMessage.includes(cityName)) {
        city = cityName.charAt(0).toUpperCase() + cityName.slice(1)
        break
      }
    }

    return {
      quantity: quantity > 0 ? quantity : undefined,
      city: city || undefined,
      userMessage: message,
    }
  }

  async validateCustomerData(data: OrderFormData): Promise<AgentResult> {
    try {
      const errors: string[] = []

      // Validate name
      if (!data.customerName || data.customerName.trim().length < 2) {
        errors.push('Customer name must be at least 2 characters long')
      }

      // Validate phone
      const phoneRegex = /^(\+92|92|0)?[0-9]{10}$/
      if (!phoneRegex.test(data.customerPhone.replace(/\s|-/g, ''))) {
        errors.push('Please provide a valid Pakistani phone number')
      }

      // Validate email if provided
      if (data.customerEmail) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(data.customerEmail)) {
          errors.push('Please provide a valid email address')
        }
      }

      // Validate address
      if (!data.customerAddress || data.customerAddress.trim().length < 10) {
        errors.push('Please provide a complete delivery address')
      }

      // Validate city
      if (!data.city || data.city.trim().length < 2) {
        errors.push('Please specify your city')
      }

      // Validate quantity
      if (!data.quantity || data.quantity <= 0 || data.quantity > 100) {
        errors.push('Quantity must be between 0.1 and 100 kg')
      }

      if (errors.length > 0) {
        await this.logAction('customer_data_validation_failed', data, { errors }, false)
        return {
          success: false,
          error: errors.join(', '),
          data: { errors },
        }
      }

      await this.logAction('customer_data_validated', data, null, true)
      return {
        success: true,
        data: data,
      }
    } catch (error) {
      return this.handleError(error, 'customer_data_validation')
    }
  }
}
