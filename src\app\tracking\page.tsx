'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Search, Package, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { config } from '@/lib/config'

export default function TrackingPage() {
  const [orderNumber, setOrderNumber] = useState('')
  const [loading, setLoading] = useState(false)

  const handleTrackOrder = () => {
    if (orderNumber.trim()) {
      window.location.href = `/tracking/${orderNumber.trim()}`
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTrackOrder()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-cream to-white">
      {/* Header */}
      <header className="bg-white shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-gradient font-heading">
              🥭 {config.app.name}
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/order" className="text-gray-600 hover:text-leaf-600 transition-colors">
                Order Now
              </Link>
              <Link href="/" className="text-gray-600 hover:text-leaf-600 transition-colors">
                Home
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <div className="w-20 h-20 bg-leaf-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Package className="w-10 h-10 text-leaf-500" />
          </div>
          
          <h1 className="text-4xl font-bold text-charcoal mb-4 font-heading">
            Track Your Order
          </h1>
          
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Enter your order number to get real-time updates on your mango delivery
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="max-w-md mx-auto"
        >
          <div className="card">
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Order Number
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={orderNumber}
                    onChange={(e) => setOrderNumber(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="input-field pr-12"
                    placeholder="MNG-XXXXXXXX"
                    autoFocus
                  />
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  You can find your order number in the confirmation email or SMS
                </p>
              </div>

              <button
                onClick={handleTrackOrder}
                disabled={!orderNumber.trim() || loading}
                className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="spinner mr-2"></div>
                    Searching...
                  </>
                ) : (
                  <>
                    Track Order
                    <ArrowRight className="inline w-4 h-4 ml-2" />
                  </>
                )}
              </button>
            </div>
          </div>
        </motion.div>

        {/* Sample Order Numbers for Demo */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mt-16"
        >
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-3">
              Demo Order Numbers
            </h3>
            <p className="text-blue-700 mb-4">
              Try these sample order numbers to see the tracking system in action:
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {[
                'MNG-DEMO-001',
                'MNG-DEMO-002',
                'MNG-DEMO-003',
                'MNG-DEMO-004'
              ].map((demoNumber) => (
                <button
                  key={demoNumber}
                  onClick={() => setOrderNumber(demoNumber)}
                  className="text-left p-3 bg-white border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors"
                >
                  <code className="text-blue-600 font-mono text-sm">{demoNumber}</code>
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* How to Track Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16"
        >
          <h2 className="text-2xl font-bold text-charcoal mb-8 text-center font-heading">
            How Order Tracking Works
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: '1',
                title: 'Order Placed',
                description: 'Your order is received and being processed by our AI system',
                icon: '📝'
              },
              {
                step: '2',
                title: 'Payment Verified',
                description: 'Payment screenshot is verified using OCR technology',
                icon: '💳'
              },
              {
                step: '3',
                title: 'Shipped',
                description: 'Your mangoes are packed and shipped via our courier partners',
                icon: '🚚'
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-mango-500 to-leaf-500 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                  {item.icon}
                </div>
                <h3 className="text-lg font-semibold text-charcoal mb-2 font-heading">
                  {item.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Contact Support */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="bg-gray-50 rounded-lg p-8">
            <h3 className="text-lg font-semibold text-charcoal mb-4 font-heading">
              Need Help?
            </h3>
            <p className="text-gray-600 mb-6">
              Can't find your order or having trouble tracking? Our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href={`tel:${config.app.supportPhone}`}
                className="btn-outline"
              >
                Call Support
              </a>
              <a
                href={`mailto:${config.app.supportEmail}`}
                className="btn-outline"
              >
                Email Support
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
