import { 
  Order, 
  OrderStatus, 
  PaymentStatus, 
  PaymentMethod, 
  VerificationStatus,
  CourierService,
  DeliveryStatus,
  AgentType 
} from '@prisma/client'

// Extended types with relations
export interface OrderWithDetails extends Order {
  invoice?: {
    id: string
    invoiceNumber: string
    pdfUrl?: string
    generatedAt: Date
  }
  payment?: {
    id: string
    method: PaymentMethod
    amount: number
    transactionId?: string
    screenshotUrl?: string
    verificationStatus: VerificationStatus
    verifiedAt?: Date
  }
  delivery?: {
    id: string
    courierService: CourierService
    trackingNumber?: string
    status: DeliveryStatus
    bookedAt?: Date
    pickedUpAt?: Date
    deliveredAt?: Date
  }
  agentLogs?: AgentLogType[]
}

export interface AgentLogType {
  id: string
  agentType: AgentType
  action: string
  input?: any
  output?: any
  success: boolean
  error?: string
  createdAt: Date
}

// Form types
export interface OrderFormData {
  customerName: string
  customerPhone: string
  customerEmail?: string
  customerAddress: string
  city: string
  quantity: number
}

export interface PaymentFormData {
  method: PaymentMethod
  transactionId?: string
  screenshot?: File
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Agent types
export interface AgentContext {
  orderId: string
  userId?: string
  metadata?: Record<string, any>
}

export interface AgentResult {
  success: boolean
  data?: any
  error?: string
  nextAction?: string
}

// Business logic types
export interface PricingConfig {
  pricePerKg: number
  deliveryCharges: {
    [city: string]: number
  }
}

export interface CourierBookingRequest {
  senderName: string
  senderPhone: string
  senderAddress: string
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  city: string
  weight: number
  pieces: number
  codAmount?: number
}

export interface CourierBookingResponse {
  success: boolean
  trackingNumber?: string
  error?: string
  estimatedDelivery?: string
}

export interface TrackingUpdate {
  status: DeliveryStatus
  location?: string
  timestamp: Date
  description?: string
}

// Dashboard types
export interface DashboardStats {
  totalOrders: number
  pendingOrders: number
  paidOrders: number
  shippedOrders: number
  deliveredOrders: number
  totalRevenue: number
  todayRevenue: number
  averageOrderValue: number
}

export interface RevenueData {
  date: string
  revenue: number
  orders: number
}

// OCR types
export interface OCRResult {
  text: string
  confidence: number
  transactionId?: string
  amount?: number
  timestamp?: Date
}

// Notification types
export interface NotificationData {
  type: 'email' | 'sms' | 'whatsapp'
  recipient: string
  subject?: string
  message: string
  orderId?: string
}

export {
  OrderStatus,
  PaymentStatus,
  PaymentMethod,
  VerificationStatus,
  CourierService,
  DeliveryStatus,
  AgentType
}
