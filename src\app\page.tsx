'use client'

import { useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  ShoppingCart, 
  Truck, 
  Shield, 
  Clock, 
  Star, 
  Phone, 
  Mail,
  MapPin,
  CheckCircle,
  Bot,
  Zap
} from 'lucide-react'
import { config } from '@/lib/config'

export default function HomePage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const features = [
    {
      icon: Bot,
      title: 'AI-Powered Ordering',
      description: 'Smart chatbot handles your order from start to finish'
    },
    {
      icon: Zap,
      title: 'Instant Processing',
      description: 'Orders processed automatically within minutes'
    },
    {
      icon: Shield,
      title: 'Secure Payments',
      description: 'Multiple payment options with OCR verification'
    },
    {
      icon: Truck,
      title: 'Fast Delivery',
      description: 'Reliable courier partners across Pakistan'
    },
    {
      icon: Clock,
      title: '24/7 Tracking',
      description: 'Real-time updates on your order status'
    },
    {
      icon: Star,
      title: 'Premium Quality',
      description: 'Fresh mangoes directly from the farm'
    }
  ]

  const testimonials = [
    {
      name: '<PERSON>',
      location: 'Karachi',
      rating: 5,
      comment: 'Amazing quality mangoes! The AI ordering system made it so easy.'
    },
    {
      name: '<PERSON><PERSON> Ali',
      location: 'Lahore',
      rating: 5,
      comment: 'Fast delivery and excellent customer service. Highly recommended!'
    },
    {
      name: 'Hassan Sheikh',
      location: 'Islamabad',
      rating: 5,
      comment: 'Best mangoes I\'ve ever tasted. The automated system is brilliant!'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-cream to-white">
      {/* Navigation */}
      <nav className="bg-white shadow-md sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="text-2xl font-bold text-gradient font-heading">
                🥭 {config.app.name}
              </div>
            </div>
            
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <a href="#features" className="text-gray-700 hover:text-leaf-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  Features
                </a>
                <a href="#how-it-works" className="text-gray-700 hover:text-leaf-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  How It Works
                </a>
                <a href="#testimonials" className="text-gray-700 hover:text-leaf-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  Reviews
                </a>
                <a href="#contact" className="text-gray-700 hover:text-leaf-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  Contact
                </a>
                <Link href="/admin" className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  Admin
                </Link>
              </div>
            </div>
            
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-700 hover:text-leaf-600 focus:outline-none"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              <a href="#features" className="text-gray-700 hover:text-leaf-600 block px-3 py-2 rounded-md text-base font-medium">
                Features
              </a>
              <a href="#how-it-works" className="text-gray-700 hover:text-leaf-600 block px-3 py-2 rounded-md text-base font-medium">
                How It Works
              </a>
              <a href="#testimonials" className="text-gray-700 hover:text-leaf-600 block px-3 py-2 rounded-md text-base font-medium">
                Reviews
              </a>
              <a href="#contact" className="text-gray-700 hover:text-leaf-600 block px-3 py-2 rounded-md text-base font-medium">
                Contact
              </a>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-5xl lg:text-6xl font-bold text-charcoal mb-6 font-heading">
                Fresh Mangoes
                <span className="text-gradient block">Delivered Smart</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {config.app.tagline}. Experience the future of fruit delivery with our AI-powered platform.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/order" className="btn-primary text-center">
                  <ShoppingCart className="inline-block w-5 h-5 mr-2" />
                  Order Now
                </Link>
                <Link href="/tracking" className="btn-outline text-center">
                  <Truck className="inline-block w-5 h-5 mr-2" />
                  Track Order
                </Link>
              </div>
              <div className="mt-8 flex items-center space-x-6 text-sm text-gray-600">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-leaf-500 mr-2" />
                  Free delivery in major cities
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-leaf-500 mr-2" />
                  24/7 AI support
                </div>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="relative z-10">
                <img
                  src="/api/placeholder/600/400"
                  alt="Fresh Mangoes"
                  className="rounded-2xl shadow-2xl animate-float"
                />
              </div>
              <div className="absolute inset-0 gradient-bg rounded-2xl transform rotate-6 scale-105 opacity-20"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-charcoal mb-4 font-heading">
              Why Choose MangoEase AI?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience the perfect blend of technology and tradition with our AI-powered mango delivery service.
            </p>
          </motion.div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card hover:shadow-xl transition-all duration-300 group"
              >
                <div className="text-leaf-500 mb-4 group-hover:scale-110 transition-transform duration-300">
                  <feature.icon className="w-12 h-12" />
                </div>
                <h3 className="text-xl font-semibold text-charcoal mb-3 font-heading">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 bg-gradient-to-r from-mango-50 to-leaf-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-charcoal mb-4 font-heading">
              How It Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our AI handles everything so you can focus on enjoying fresh mangoes.
            </p>
          </motion.div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: '1', title: 'Place Order', description: 'Tell our AI what you want' },
              { step: '2', title: 'AI Processing', description: 'Smart system handles everything' },
              { step: '3', title: 'Payment & Booking', description: 'Secure payment & courier booking' },
              { step: '4', title: 'Delivery', description: 'Fresh mangoes at your door' }
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-mango-500 to-leaf-500 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold text-charcoal mb-2 font-heading">
                  {step.title}
                </h3>
                <p className="text-gray-600">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 gradient-bg">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-white mb-6 font-heading">
              Ready for Fresh Mangoes?
            </h2>
            <p className="text-xl text-white/90 mb-8">
              Join thousands of satisfied customers who trust MangoEase AI for their mango needs.
            </p>
            <Link href="/order" className="bg-white text-leaf-700 hover:bg-gray-100 font-bold py-4 px-8 rounded-lg text-lg transition-all duration-200 shadow-lg hover:shadow-xl inline-flex items-center">
              <ShoppingCart className="w-6 h-6 mr-3" />
              Start Your Order
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="bg-charcoal text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="text-2xl font-bold mb-4 font-heading">
                🥭 {config.app.name}
              </div>
              <p className="text-gray-300 mb-6 max-w-md">
                {config.app.tagline}. Fresh mangoes delivered with cutting-edge AI technology.
              </p>
              <div className="flex space-x-4">
                <a href={`tel:${config.app.supportPhone}`} className="flex items-center text-gray-300 hover:text-white transition-colors">
                  <Phone className="w-5 h-5 mr-2" />
                  {config.app.supportPhone}
                </a>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4 font-heading">Quick Links</h3>
              <ul className="space-y-2">
                <li><Link href="/order" className="text-gray-300 hover:text-white transition-colors">Order Now</Link></li>
                <li><Link href="/tracking" className="text-gray-300 hover:text-white transition-colors">Track Order</Link></li>
                <li><a href="#features" className="text-gray-300 hover:text-white transition-colors">Features</a></li>
                <li><a href="#how-it-works" className="text-gray-300 hover:text-white transition-colors">How It Works</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4 font-heading">Contact</h3>
              <ul className="space-y-2">
                <li className="flex items-center text-gray-300">
                  <Mail className="w-4 h-4 mr-2" />
                  {config.app.supportEmail}
                </li>
                <li className="flex items-center text-gray-300">
                  <MapPin className="w-4 h-4 mr-2" />
                  Pakistan
                </li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 {config.app.name}. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
