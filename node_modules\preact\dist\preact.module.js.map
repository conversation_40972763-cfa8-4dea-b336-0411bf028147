{"version": 3, "file": "preact.module.js", "sources": ["../src/constants.js", "../src/util.js", "../src/options.js", "../src/create-element.js", "../src/component.js", "../src/diff/props.js", "../src/create-context.js", "../src/diff/children.js", "../src/diff/index.js", "../src/render.js", "../src/clone-element.js", "../src/diff/catch-error.js"], "sourcesContent": ["/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 2;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 1;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\nexport const XHTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\nexport const MATH_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n\nexport const NULL = null;\nexport const UNDEFINED = undefined;\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { EMPTY_ARR } from './constants';\n\nexport const isArray = Array.isArray;\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\t// @ts-expect-error We change the type of `obj` to be `O & P`\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {import('./index').ContainerNode} node The node to remove\n */\nexport function removeNode(node) {\n\tif (node && node.parentNode) node.parentNode.removeChild(node);\n}\n\nexport const slice = EMPTY_ARR.slice;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {import('./internal').Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import { slice } from './util';\nimport options from './options';\nimport { NULL, UNDEFINED } from './constants';\n\nlet vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor for this\n * virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the\n * virtual node\n * @returns {import('./internal').VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != NULL) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] === UNDEFINED) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(type, normalizedProps, key, ref, NULL);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {import('./internal').VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\t/** @type {import('./internal').VNode} */\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: NULL,\n\t\t_parent: NULL,\n\t\t_depth: 0,\n\t\t_dom: NULL,\n\t\t_component: NULL,\n\t\tconstructor: UNDEFINED,\n\t\t_original: original == NULL ? ++vnodeId : original,\n\t\t_index: -1,\n\t\t_flags: 0\n\t};\n\n\t// Only invoke the vnode hook if this was *not* a direct copy:\n\tif (original == NULL && options.vnode != NULL) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: NULL };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != NULL && vnode.constructor == UNDEFINED;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\nimport { MODE_HYDRATE, NULL } from './constants';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function BaseComponent(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {import('./internal').Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nBaseComponent.prototype.setState = function (update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState != NULL && this._nextState != this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\t// Some libraries like `immer` mark the current state as readonly,\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\n\t\tupdate = update(assign({}, s), this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == NULL) return;\n\n\tif (this._vnode) {\n\t\tif (callback) {\n\t\t\tthis._stateCallbacks.push(callback);\n\t\t}\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {import('./internal').Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nBaseComponent.prototype.forceUpdate = function (callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](https://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {ComponentChildren | void}\n */\nBaseComponent.prototype.render = Fragment;\n\n/**\n * @param {import('./internal').VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == NULL) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._index + 1)\n\t\t\t: NULL;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != NULL && sibling._dom != NULL) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : NULL;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {import('./internal').Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet oldVNode = component._vnode,\n\t\toldDom = oldVNode._dom,\n\t\tcommitQueue = [],\n\t\trefQueue = [];\n\n\tif (component._parentDom) {\n\t\tconst newVNode = assign({}, oldVNode);\n\t\tnewVNode._original = oldVNode._original + 1;\n\t\tif (options.vnode) options.vnode(newVNode);\n\n\t\tdiff(\n\t\t\tcomponent._parentDom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tcomponent._parentDom.namespaceURI,\n\t\t\toldVNode._flags & MODE_HYDRATE ? [oldDom] : NULL,\n\t\t\tcommitQueue,\n\t\t\toldDom == NULL ? getDomSibling(oldVNode) : oldDom,\n\t\t\t!!(oldVNode._flags & MODE_HYDRATE),\n\t\t\trefQueue\n\t\t);\n\n\t\tnewVNode._original = oldVNode._original;\n\t\tnewVNode._parent._children[newVNode._index] = newVNode;\n\t\tcommitRoot(commitQueue, newVNode, refQueue);\n\n\t\tif (newVNode._dom != oldDom) {\n\t\t\tupdateParentDomPointers(newVNode);\n\t\t}\n\t}\n}\n\n/**\n * @param {import('./internal').VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != NULL && vnode._component != NULL) {\n\t\tvnode._dom = vnode._component.base = NULL;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != NULL && child._dom != NULL) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<import('./internal').Component>}\n */\nlet rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\nconst defer =\n\ttypeof Promise == 'function'\n\t\t? Promise.prototype.then.bind(Promise.resolve())\n\t\t: setTimeout;\n\n/**\n * Enqueue a rerender of a component\n * @param {import('./internal').Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce != options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || defer)(process);\n\t}\n}\n\n/**\n * @param {import('./internal').Component} a\n * @param {import('./internal').Component} b\n */\nconst depthSort = (a, b) => a._vnode._depth - b._vnode._depth;\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet c,\n\t\tl = 1;\n\n\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t// process() calls from getting scheduled while `queue` is still being consumed.\n\twhile (rerenderQueue.length) {\n\t\t// Keep the rerender queue sorted by (depth, insertion order). The queue\n\t\t// will initially be sorted on the first iteration only if it has more than 1 item.\n\t\t//\n\t\t// New items can be added to the queue e.g. when rerendering a provider, so we want to\n\t\t// keep the order from top to bottom with those new items so we can handle them in a\n\t\t// single pass\n\t\tif (rerenderQueue.length > l) {\n\t\t\trerenderQueue.sort(depthSort);\n\t\t}\n\n\t\tc = rerenderQueue.shift();\n\t\tl = rerenderQueue.length;\n\n\t\tif (c._dirty) {\n\t\t\trenderComponent(c);\n\t\t}\n\t}\n\tprocess._rerenderCount = 0;\n}\n\nprocess._rerenderCount = 0;\n", "import { IS_NON_DIMENSIONAL, NULL, SVG_NAMESPACE } from '../constants';\nimport options from '../options';\n\nfunction setStyle(style, key, value) {\n\tif (key[0] == '-') {\n\t\tstyle.setProperty(key, value == NULL ? '' : value);\n\t} else if (value == NULL) {\n\t\tstyle[key] = '';\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n\t\tstyle[key] = value;\n\t} else {\n\t\tstyle[key] = value + 'px';\n\t}\n}\n\nconst CAPTURE_REGEX = /(PointerCapture)$|Capture$/i;\n\n// A logical clock to solve issues like https://github.com/preactjs/preact/issues/3927.\n// When the DOM performs an event it leaves micro-ticks in between bubbling up which means that\n// an event can trigger on a newly reated DOM-node while the event bubbles up.\n//\n// Originally inspired by Vue\n// (https://github.com/vuejs/core/blob/caeb8a68811a1b0f79/packages/runtime-dom/src/modules/events.ts#L90-L101),\n// but modified to use a logical clock instead of Date.now() in case event handlers get attached\n// and events get dispatched during the same millisecond.\n//\n// The clock is incremented after each new event dispatch. This allows 1 000 000 new events\n// per second for over 280 years before the value reaches Number.MAX_SAFE_INTEGER (2**53 - 1).\nlet eventClock = 0;\n\n/**\n * Set a property value on a DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {string} namespace Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, namespace) {\n\tlet useCapture;\n\n\to: if (name == 'style') {\n\t\tif (typeof value == 'string') {\n\t\t\tdom.style.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\tdom.style.cssText = oldValue = '';\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (name in oldValue) {\n\t\t\t\t\tif (!(value && name in value)) {\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (name in value) {\n\t\t\t\t\tif (!oldValue || value[name] != oldValue[name]) {\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] == 'o' && name[1] == 'n') {\n\t\tuseCapture = name != (name = name.replace(CAPTURE_REGEX, '$1'));\n\t\tconst lowerCaseName = name.toLowerCase();\n\n\t\t// Infer correct casing for DOM built-in events:\n\t\tif (lowerCaseName in dom || name == 'onFocusOut' || name == 'onFocusIn')\n\t\t\tname = lowerCaseName.slice(2);\n\t\telse name = name.slice(2);\n\n\t\tif (!dom._listeners) dom._listeners = {};\n\t\tdom._listeners[name + useCapture] = value;\n\n\t\tif (value) {\n\t\t\tif (!oldValue) {\n\t\t\t\tvalue._attached = eventClock;\n\t\t\t\tdom.addEventListener(\n\t\t\t\t\tname,\n\t\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\t\tuseCapture\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tvalue._attached = oldValue._attached;\n\t\t\t}\n\t\t} else {\n\t\t\tdom.removeEventListener(\n\t\t\t\tname,\n\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\tuseCapture\n\t\t\t);\n\t\t}\n\t} else {\n\t\tif (namespace == SVG_NAMESPACE) {\n\t\t\t// Normalize incorrect prop usage for SVG:\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n\t\t\t// - className --> class\n\t\t\tname = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n\t\t} else if (\n\t\t\tname != 'width' &&\n\t\t\tname != 'height' &&\n\t\t\tname != 'href' &&\n\t\t\tname != 'list' &&\n\t\t\tname != 'form' &&\n\t\t\t// Default value in browsers is `-1` and an empty string is\n\t\t\t// cast to `0` instead\n\t\t\tname != 'tabIndex' &&\n\t\t\tname != 'download' &&\n\t\t\tname != 'rowSpan' &&\n\t\t\tname != 'colSpan' &&\n\t\t\tname != 'role' &&\n\t\t\tname != 'popover' &&\n\t\t\tname in dom\n\t\t) {\n\t\t\ttry {\n\t\t\t\tdom[name] = value == NULL ? '' : value;\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\n\t\t\t\tbreak o;\n\t\t\t} catch (e) {}\n\t\t}\n\n\t\t// aria- and data- attributes have no boolean representation.\n\t\t// A `false` value is different from the attribute not being\n\t\t// present, so we can't remove it. For non-boolean aria\n\t\t// attributes we could treat false as a removal, but the\n\t\t// amount of exceptions would cost too many bytes. On top of\n\t\t// that other frameworks generally stringify `false`.\n\n\t\tif (typeof value == 'function') {\n\t\t\t// never serialize functions as attribute values\n\t\t} else if (value != NULL && (value !== false || name[4] == '-')) {\n\t\t\tdom.setAttribute(name, name == 'popover' && value == true ? '' : value);\n\t\t} else {\n\t\t\tdom.removeAttribute(name);\n\t\t}\n\t}\n}\n\n/**\n * Create an event proxy function.\n * @param {boolean} useCapture Is the event handler for the capture phase.\n * @private\n */\nfunction createEventProxy(useCapture) {\n\t/**\n\t * Proxy an event to hooked event handlers\n\t * @param {import('../internal').PreactEvent} e The event object from the browser\n\t * @private\n\t */\n\treturn function (e) {\n\t\tif (this._listeners) {\n\t\t\tconst eventHandler = this._listeners[e.type + useCapture];\n\t\t\tif (e._dispatched == NULL) {\n\t\t\t\te._dispatched = eventClock++;\n\n\t\t\t\t// When `e._dispatched` is smaller than the time when the targeted event\n\t\t\t\t// handler was attached we know we have bubbled up to an element that was added\n\t\t\t\t// during patching the DOM.\n\t\t\t} else if (e._dispatched < eventHandler._attached) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\treturn eventHandler(options.event ? options.event(e) : e);\n\t\t}\n\t};\n}\n\nconst eventProxy = createEventProxy(false);\nconst eventProxyCapture = createEventProxy(true);\n", "import { enqueueRender } from './component';\nimport { NULL } from './constants';\n\nexport let i = 0;\n\nexport function createContext(defaultValue) {\n\tfunction Context(props) {\n\t\tif (!this.getChildContext) {\n\t\t\t/** @type {Set<import('./internal').Component> | null} */\n\t\t\tlet subs = new Set();\n\t\t\tlet ctx = {};\n\t\t\tctx[Context._id] = this;\n\n\t\t\tthis.getChildContext = () => ctx;\n\n\t\t\tthis.componentWillUnmount = () => {\n\t\t\t\tsubs = NULL;\n\t\t\t};\n\n\t\t\tthis.shouldComponentUpdate = function (_props) {\n\t\t\t\t// @ts-expect-error even\n\t\t\t\tif (this.props.value != _props.value) {\n\t\t\t\t\tsubs.forEach(c => {\n\t\t\t\t\t\tc._force = true;\n\t\t\t\t\t\tenqueueRender(c);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tthis.sub = c => {\n\t\t\t\tsubs.add(c);\n\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\tif (subs) {\n\t\t\t\t\t\tsubs.delete(c);\n\t\t\t\t\t}\n\t\t\t\t\tif (old) old.call(c);\n\t\t\t\t};\n\t\t\t};\n\t\t}\n\n\t\treturn props.children;\n\t}\n\n\tContext._id = '__cC' + i++;\n\tContext._defaultValue = defaultValue;\n\n\t/** @type {import('./internal').FunctionComponent} */\n\tContext.Consumer = (props, contextValue) => {\n\t\treturn props.children(contextValue);\n\t};\n\n\t// we could also get rid of _contextRef entirely\n\tContext.Provider =\n\t\tContext._contextRef =\n\t\tContext.Consumer.contextType =\n\t\t\tContext;\n\n\treturn Context;\n}\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport {\n\tEMPTY_OBJ,\n\tEMPTY_ARR,\n\tINSERT_VNODE,\n\tMATCHED,\n\tUNDEFINED,\n\tNULL\n} from '../constants';\nimport { isArray } from '../util';\nimport { getDomSibling } from '../component';\n\n/**\n * @typedef {import('../internal').ComponentChildren} ComponentChildren\n * @typedef {import('../internal').Component} Component\n * @typedef {import('../internal').PreactElement} PreactElement\n * @typedef {import('../internal').VNode} VNode\n */\n\n/**\n * Diff the children of a virtual node\n * @param {PreactElement} parentDom The DOM element whose children are being\n * diffed\n * @param {ComponentChildren[]} renderResult\n * @param {VNode} newParentVNode The new virtual node whose children should be\n * diff'ed against oldParentVNode\n * @param {VNode} oldParentVNode The old virtual node whose children should be\n * diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\tlet i,\n\t\t/** @type {VNode} */\n\t\toldVNode,\n\t\t/** @type {VNode} */\n\t\tchildVNode,\n\t\t/** @type {PreactElement} */\n\t\tnewDom,\n\t\t/** @type {PreactElement} */\n\t\tfirstChildDom;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\t/** @type {VNode[]} */\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet newChildrenLength = renderResult.length;\n\n\toldDom = constructNewChildrenArray(\n\t\tnewParentVNode,\n\t\trenderResult,\n\t\toldChildren,\n\t\toldDom,\n\t\tnewChildrenLength\n\t);\n\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\tchildVNode = newParentVNode._children[i];\n\t\tif (childVNode == NULL) continue;\n\n\t\t// At this point, constructNewChildrenArray has assigned _index to be the\n\t\t// matchingIndex for this VNode's oldVNode (or -1 if there is no oldVNode).\n\t\tif (childVNode._index == -1) {\n\t\t\toldVNode = EMPTY_OBJ;\n\t\t} else {\n\t\t\toldVNode = oldChildren[childVNode._index] || EMPTY_OBJ;\n\t\t}\n\n\t\t// Update childVNode._index to its final index\n\t\tchildVNode._index = i;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tlet result = diff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\n\t\t// Adjust DOM nodes\n\t\tnewDom = childVNode._dom;\n\t\tif (childVNode.ref && oldVNode.ref != childVNode.ref) {\n\t\t\tif (oldVNode.ref) {\n\t\t\t\tapplyRef(oldVNode.ref, NULL, childVNode);\n\t\t\t}\n\t\t\trefQueue.push(\n\t\t\t\tchildVNode.ref,\n\t\t\t\tchildVNode._component || newDom,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t}\n\n\t\tif (firstChildDom == NULL && newDom != NULL) {\n\t\t\tfirstChildDom = newDom;\n\t\t}\n\n\t\tif (\n\t\t\tchildVNode._flags & INSERT_VNODE ||\n\t\t\toldVNode._children === childVNode._children\n\t\t) {\n\t\t\toldDom = insert(childVNode, oldDom, parentDom);\n\t\t} else if (typeof childVNode.type == 'function' && result !== UNDEFINED) {\n\t\t\toldDom = result;\n\t\t} else if (newDom) {\n\t\t\toldDom = newDom.nextSibling;\n\t\t}\n\n\t\t// Unset diffing flags\n\t\tchildVNode._flags &= ~(INSERT_VNODE | MATCHED);\n\t}\n\n\tnewParentVNode._dom = firstChildDom;\n\n\treturn oldDom;\n}\n\n/**\n * @param {VNode} newParentVNode\n * @param {ComponentChildren[]} renderResult\n * @param {VNode[]} oldChildren\n */\nfunction constructNewChildrenArray(\n\tnewParentVNode,\n\trenderResult,\n\toldChildren,\n\toldDom,\n\tnewChildrenLength\n) {\n\t/** @type {number} */\n\tlet i;\n\t/** @type {VNode} */\n\tlet childVNode;\n\t/** @type {VNode} */\n\tlet oldVNode;\n\n\tlet oldChildrenLength = oldChildren.length,\n\t\tremainingOldChildren = oldChildrenLength;\n\n\tlet skew = 0;\n\n\tnewParentVNode._children = new Array(newChildrenLength);\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\t// @ts-expect-error We are reusing the childVNode variable to hold both the\n\t\t// pre and post normalized childVNode\n\t\tchildVNode = renderResult[i];\n\n\t\tif (\n\t\t\tchildVNode == NULL ||\n\t\t\ttypeof childVNode == 'boolean' ||\n\t\t\ttypeof childVNode == 'function'\n\t\t) {\n\t\t\tnewParentVNode._children[i] = NULL;\n\t\t\tcontinue;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (\n\t\t\ttypeof childVNode == 'string' ||\n\t\t\ttypeof childVNode == 'number' ||\n\t\t\t// eslint-disable-next-line valid-typeof\n\t\t\ttypeof childVNode == 'bigint' ||\n\t\t\tchildVNode.constructor == String\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tNULL,\n\t\t\t\tchildVNode,\n\t\t\t\tNULL,\n\t\t\t\tNULL,\n\t\t\t\tNULL\n\t\t\t);\n\t\t} else if (isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tNULL,\n\t\t\t\tNULL,\n\t\t\t\tNULL\n\t\t\t);\n\t\t} else if (childVNode.constructor == UNDEFINED && childVNode._depth > 0) {\n\t\t\t// VNode is already in use, clone it. This can happen in the following\n\t\t\t// scenario:\n\t\t\t//   const reuse = <div />\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tchildVNode.ref ? childVNode.ref : NULL,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\tconst skewedIndex = i + skew;\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Temporarily store the matchingIndex on the _index property so we can pull\n\t\t// out the oldVNode in diffChildren. We'll override this to the VNode's\n\t\t// final index after using this property to get the oldVNode\n\t\tconst matchingIndex = (childVNode._index = findMatchingIndex(\n\t\t\tchildVNode,\n\t\t\toldChildren,\n\t\t\tskewedIndex,\n\t\t\tremainingOldChildren\n\t\t));\n\n\t\toldVNode = NULL;\n\t\tif (matchingIndex != -1) {\n\t\t\toldVNode = oldChildren[matchingIndex];\n\t\t\tremainingOldChildren--;\n\t\t\tif (oldVNode) {\n\t\t\t\toldVNode._flags |= MATCHED;\n\t\t\t}\n\t\t}\n\n\t\t// Here, we define isMounting for the purposes of the skew diffing\n\t\t// algorithm. Nodes that are unsuspending are considered mounting and we detect\n\t\t// this by checking if oldVNode._original == null\n\t\tconst isMounting = oldVNode == NULL || oldVNode._original == NULL;\n\n\t\tif (isMounting) {\n\t\t\tif (matchingIndex == -1) {\n\t\t\t\t// When the array of children is growing we need to decrease the skew\n\t\t\t\t// as we are adding a new element to the array.\n\t\t\t\t// Example:\n\t\t\t\t// [1, 2, 3] --> [0, 1, 2, 3]\n\t\t\t\t// oldChildren   newChildren\n\t\t\t\t//\n\t\t\t\t// The new element is at index 0, so our skew is 0,\n\t\t\t\t// we need to decrease the skew as we are adding a new element.\n\t\t\t\t// The decrease will cause us to compare the element at position 1\n\t\t\t\t// with value 1 with the element at position 0 with value 0.\n\t\t\t\t//\n\t\t\t\t// A linear concept is applied when the array is shrinking,\n\t\t\t\t// if the length is unchanged we can assume that no skew\n\t\t\t\t// changes are needed.\n\t\t\t\tif (newChildrenLength > oldChildrenLength) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else if (newChildrenLength < oldChildrenLength) {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If we are mounting a DOM VNode, mark it for insertion\n\t\t\tif (typeof childVNode.type != 'function') {\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t} else if (matchingIndex != skewedIndex) {\n\t\t\t// When we move elements around i.e. [0, 1, 2] --> [1, 0, 2]\n\t\t\t// --> we diff 1, we find it at position 1 while our skewed index is 0 and our skew is 0\n\t\t\t//     we set the skew to 1 as we found an offset.\n\t\t\t// --> we diff 0, we find it at position 0 while our skewed index is at 2 and our skew is 1\n\t\t\t//     this makes us increase the skew again.\n\t\t\t// --> we diff 2, we find it at position 2 while our skewed index is at 4 and our skew is 2\n\t\t\t//\n\t\t\t// this becomes an optimization question where currently we see a 1 element offset as an insertion\n\t\t\t// or deletion i.e. we optimize for [0, 1, 2] --> [9, 0, 1, 2]\n\t\t\t// while a more than 1 offset we see as a swap.\n\t\t\t// We could probably build heuristics for having an optimized course of action here as well, but\n\t\t\t// might go at the cost of some bytes.\n\t\t\t//\n\t\t\t// If we wanted to optimize for i.e. only swaps we'd just do the last two code-branches and have\n\t\t\t// only the first item be a re-scouting and all the others fall in their skewed counter-part.\n\t\t\t// We could also further optimize for swaps\n\t\t\tif (matchingIndex == skewedIndex - 1) {\n\t\t\t\tskew--;\n\t\t\t} else if (matchingIndex == skewedIndex + 1) {\n\t\t\t\tskew++;\n\t\t\t} else {\n\t\t\t\tif (matchingIndex > skewedIndex) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\n\t\t\t\t// Move this VNode's DOM if the original index (matchingIndex) doesn't\n\t\t\t\t// match the new skew index (i + new skew)\n\t\t\t\t// In the former two branches we know that it matches after skewing\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Remove remaining oldChildren if there are any. Loop forwards so that as we\n\t// unmount DOM from the beginning of the oldChildren, we can adjust oldDom to\n\t// point to the next child, which needs to be the first DOM node that won't be\n\t// unmounted.\n\tif (remainingOldChildren) {\n\t\tfor (i = 0; i < oldChildrenLength; i++) {\n\t\t\toldVNode = oldChildren[i];\n\t\t\tif (oldVNode != NULL && (oldVNode._flags & MATCHED) == 0) {\n\t\t\t\tif (oldVNode._dom == oldDom) {\n\t\t\t\t\toldDom = getDomSibling(oldVNode);\n\t\t\t\t}\n\n\t\t\t\tunmount(oldVNode, oldVNode);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn oldDom;\n}\n\n/**\n * @param {VNode} parentVNode\n * @param {PreactElement} oldDom\n * @param {PreactElement} parentDom\n * @returns {PreactElement}\n */\nfunction insert(parentVNode, oldDom, parentDom) {\n\t// Note: VNodes in nested suspended trees may be missing _children.\n\n\tif (typeof parentVNode.type == 'function') {\n\t\tlet children = parentVNode._children;\n\t\tfor (let i = 0; children && i < children.length; i++) {\n\t\t\tif (children[i]) {\n\t\t\t\t// If we enter this code path on sCU bailout, where we copy\n\t\t\t\t// oldVNode._children to newVNode._children, we need to update the old\n\t\t\t\t// children's _parent pointer to point to the newVNode (parentVNode\n\t\t\t\t// here).\n\t\t\t\tchildren[i]._parent = parentVNode;\n\t\t\t\toldDom = insert(children[i], oldDom, parentDom);\n\t\t\t}\n\t\t}\n\n\t\treturn oldDom;\n\t} else if (parentVNode._dom != oldDom) {\n\t\tif (oldDom && parentVNode.type && !parentDom.contains(oldDom)) {\n\t\t\toldDom = getDomSibling(parentVNode);\n\t\t}\n\t\tparentDom.insertBefore(parentVNode._dom, oldDom || NULL);\n\t\toldDom = parentVNode._dom;\n\t}\n\n\tdo {\n\t\toldDom = oldDom && oldDom.nextSibling;\n\t} while (oldDom != NULL && oldDom.nodeType == 8);\n\n\treturn oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {ComponentChildren} children The unflattened children of a virtual\n * node\n * @returns {VNode[]}\n */\nexport function toChildArray(children, out) {\n\tout = out || [];\n\tif (children == NULL || typeof children == 'boolean') {\n\t} else if (isArray(children)) {\n\t\tchildren.some(child => {\n\t\t\ttoChildArray(child, out);\n\t\t});\n\t} else {\n\t\tout.push(children);\n\t}\n\treturn out;\n}\n\n/**\n * @param {VNode} childVNode\n * @param {VNode[]} oldChildren\n * @param {number} skewedIndex\n * @param {number} remainingOldChildren\n * @returns {number}\n */\nfunction findMatchingIndex(\n\tchildVNode,\n\toldChildren,\n\tskewedIndex,\n\tremainingOldChildren\n) {\n\tconst key = childVNode.key;\n\tconst type = childVNode.type;\n\tlet oldVNode = oldChildren[skewedIndex];\n\n\t// We only need to perform a search if there are more children\n\t// (remainingOldChildren) to search. However, if the oldVNode we just looked\n\t// at skewedIndex was not already used in this diff, then there must be at\n\t// least 1 other (so greater than 1) remainingOldChildren to attempt to match\n\t// against. So the following condition checks that ensuring\n\t// remainingOldChildren > 1 if the oldVNode is not already used/matched. Else\n\t// if the oldVNode was null or matched, then there could needs to be at least\n\t// 1 (aka `remainingOldChildren > 0`) children to find and compare against.\n\t//\n\t// If there is an unkeyed functional VNode, that isn't a built-in like our Fragment,\n\t// we should not search as we risk re-using state of an unrelated VNode. (reverted for now)\n\tlet shouldSearch =\n\t\t// (typeof type != 'function' || type === Fragment || key) &&\n\t\tremainingOldChildren >\n\t\t(oldVNode != NULL && (oldVNode._flags & MATCHED) == 0 ? 1 : 0);\n\n\tif (\n\t\t(oldVNode === NULL && childVNode.key == null) ||\n\t\t(oldVNode &&\n\t\t\tkey == oldVNode.key &&\n\t\t\ttype == oldVNode.type &&\n\t\t\t(oldVNode._flags & MATCHED) == 0)\n\t) {\n\t\treturn skewedIndex;\n\t} else if (shouldSearch) {\n\t\tlet x = skewedIndex - 1;\n\t\tlet y = skewedIndex + 1;\n\t\twhile (x >= 0 || y < oldChildren.length) {\n\t\t\tif (x >= 0) {\n\t\t\t\toldVNode = oldChildren[x];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) == 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype == oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn x;\n\t\t\t\t}\n\t\t\t\tx--;\n\t\t\t}\n\n\t\t\tif (y < oldChildren.length) {\n\t\t\t\toldVNode = oldChildren[y];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) == 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype == oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn y;\n\t\t\t\t}\n\t\t\t\ty++;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn -1;\n}\n", "import {\n\tEMPTY_OBJ,\n\tMATH_NAMESPACE,\n\tMODE_HYDRATE,\n\tMODE_SUSPENDED,\n\tNULL,\n\tRESET_MODE,\n\tSVG_NAMESPACE,\n\tUNDEFINED,\n\tXHTML_NAMESPACE\n} from '../constants';\nimport { BaseComponent, getDomSibling } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { setProperty } from './props';\nimport { assign, isArray, removeNode, slice } from '../util';\nimport options from '../options';\n\n/**\n * @typedef {import('../internal').ComponentChildren} ComponentChildren\n * @typedef {import('../internal').Component} Component\n * @typedef {import('../internal').PreactElement} PreactElement\n * @typedef {import('../internal').VNode} VNode\n */\n\n/**\n * @template {any} T\n * @typedef {import('../internal').Ref<T>} Ref<T>\n */\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {PreactElement} parentDom The parent of the DOM element\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\t/** @type {any} */\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor != UNDEFINED) return NULL;\n\n\t// If the previous diff bailed out, resume creating/hydrating.\n\tif (oldVNode._flags & MODE_SUSPENDED) {\n\t\tisHydrating = !!(oldVNode._flags & MODE_HYDRATE);\n\t\toldDom = newVNode._dom = oldVNode._dom;\n\t\texcessDomChildren = [oldDom];\n\t}\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\touter: if (typeof newType == 'function') {\n\t\ttry {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\t\t\tconst isClassComponent =\n\t\t\t\t'prototype' in newType && newType.prototype.render;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif (isClassComponent) {\n\t\t\t\t\t// @ts-expect-error The check above verifies that newType is suppose to be constructed\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-expect-error Trust me, Component implements the interface we want\n\t\t\t\t\tnewVNode._component = c = new BaseComponent(\n\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (isClassComponent && c._nextState == NULL) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\n\t\t\tif (isClassComponent && newType.getDerivedStateFromProps != NULL) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\t\t\tc._vnode = newVNode;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == NULL &&\n\t\t\t\t\tc.componentWillMount != NULL\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidMount != NULL) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == NULL &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != NULL\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t(!c._force &&\n\t\t\t\t\t\tc.shouldComponentUpdate != NULL &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\tnewVNode._original == oldVNode._original\n\t\t\t\t) {\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original != oldVNode._original) {\n\t\t\t\t\t\t// When we are dealing with a bail because of sCU we have to update\n\t\t\t\t\t\t// the props, state and dirty-state.\n\t\t\t\t\t\t// when we are dealing with strict-equality we don't as the child could still\n\t\t\t\t\t\t// be dirtied see #3883\n\t\t\t\t\t\tc.props = newProps;\n\t\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t\tc._dirty = false;\n\t\t\t\t\t}\n\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tnewVNode._children.some(vnode => {\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\n\t\t\t\t\t});\n\n\t\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t\t}\n\t\t\t\t\tc._stateCallbacks = [];\n\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != NULL) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidUpdate != NULL) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc._parentDom = parentDom;\n\t\t\tc._force = false;\n\n\t\t\tlet renderHook = options._render,\n\t\t\t\tcount = 0;\n\t\t\tif (isClassComponent) {\n\t\t\t\tc.state = c._nextState;\n\t\t\t\tc._dirty = false;\n\n\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t}\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t} else {\n\t\t\t\tdo {\n\t\t\t\t\tc._dirty = false;\n\t\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\t\t// Handle setState called in render, see #2553\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t} while (c._dirty && ++count < 25);\n\t\t\t}\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != NULL) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (isClassComponent && !isNew && c.getSnapshotBeforeUpdate != NULL) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != NULL && tmp.type === Fragment && tmp.key == NULL;\n\t\t\tlet renderResult = tmp;\n\n\t\t\tif (isTopLevelFragment) {\n\t\t\t\trenderResult = cloneNode(tmp.props.children);\n\t\t\t}\n\n\t\t\toldDom = diffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tisArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnamespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\n\t\t\tnewVNode._flags &= RESET_MODE;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = NULL;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tnewVNode._original = NULL;\n\t\t\t// if hydrating or creating initial tree, bailout preserves DOM:\n\t\t\tif (isHydrating || excessDomChildren != NULL) {\n\t\t\t\tif (e.then) {\n\t\t\t\t\tnewVNode._flags |= isHydrating\n\t\t\t\t\t\t? MODE_HYDRATE | MODE_SUSPENDED\n\t\t\t\t\t\t: MODE_SUSPENDED;\n\n\t\t\t\t\twhile (oldDom && oldDom.nodeType == 8 && oldDom.nextSibling) {\n\t\t\t\t\t\toldDom = oldDom.nextSibling;\n\t\t\t\t\t}\n\n\t\t\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = NULL;\n\t\t\t\t\tnewVNode._dom = oldDom;\n\t\t\t\t} else {\n\t\t\t\t\tfor (let i = excessDomChildren.length; i--; ) {\n\t\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t}\n\t\t\toptions._catchError(e, newVNode, oldVNode);\n\t\t}\n\t} else if (\n\t\texcessDomChildren == NULL &&\n\t\tnewVNode._original == oldVNode._original\n\t) {\n\t\tnewVNode._children = oldVNode._children;\n\t\tnewVNode._dom = oldVNode._dom;\n\t} else {\n\t\toldDom = newVNode._dom = diffElementNodes(\n\t\t\toldVNode._dom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\t}\n\n\tif ((tmp = options.diffed)) tmp(newVNode);\n\n\treturn newVNode._flags & MODE_SUSPENDED ? undefined : oldDom;\n}\n\n/**\n * @param {Array<Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {VNode} root\n */\nexport function commitRoot(commitQueue, root, refQueue) {\n\tfor (let i = 0; i < refQueue.length; i++) {\n\t\tapplyRef(refQueue[i], refQueue[++i], refQueue[++i]);\n\t}\n\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\t// @ts-expect-error Reuse the commitQueue variable here so the type changes\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\t// @ts-expect-error See above comment on commitQueue\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\nfunction cloneNode(node) {\n\tif (\n\t\ttypeof node != 'object' ||\n\t\tnode == NULL ||\n\t\t(node._depth && node._depth > 0)\n\t) {\n\t\treturn node;\n\t}\n\n\tif (isArray(node)) {\n\t\treturn node.map(cloneNode);\n\t}\n\n\treturn assign({}, node);\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {PreactElement} dom The DOM element representing the virtual nodes\n * being diffed\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n * @returns {PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating,\n\trefQueue\n) {\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\tlet nodeType = /** @type {string} */ (newVNode.type);\n\t/** @type {any} */\n\tlet i;\n\t/** @type {{ __html?: string }} */\n\tlet newHtml;\n\t/** @type {{ __html?: string }} */\n\tlet oldHtml;\n\t/** @type {ComponentChildren} */\n\tlet newChildren;\n\tlet value;\n\tlet inputValue;\n\tlet checked;\n\n\t// Tracks entering and exiting namespaces when descending through the tree.\n\tif (nodeType == 'svg') namespace = SVG_NAMESPACE;\n\telse if (nodeType == 'math') namespace = MATH_NAMESPACE;\n\telse if (!namespace) namespace = XHTML_NAMESPACE;\n\n\tif (excessDomChildren != NULL) {\n\t\tfor (i = 0; i < excessDomChildren.length; i++) {\n\t\t\tvalue = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tvalue &&\n\t\t\t\t'setAttribute' in value == !!nodeType &&\n\t\t\t\t(nodeType ? value.localName == nodeType : value.nodeType == 3)\n\t\t\t) {\n\t\t\t\tdom = value;\n\t\t\t\texcessDomChildren[i] = NULL;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == NULL) {\n\t\tif (nodeType == NULL) {\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tdom = document.createElementNS(\n\t\t\tnamespace,\n\t\t\tnodeType,\n\t\t\tnewProps.is && newProps\n\t\t);\n\n\t\t// we are creating a new node, so we can assume this is a new subtree (in\n\t\t// case we are hydrating), this deopts the hydrate\n\t\tif (isHydrating) {\n\t\t\tif (options._hydrationMismatch)\n\t\t\t\toptions._hydrationMismatch(newVNode, excessDomChildren);\n\t\t\tisHydrating = false;\n\t\t}\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = NULL;\n\t}\n\n\tif (nodeType == NULL) {\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data != newProps)) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\t// If we are in a situation where we are not hydrating but are using\n\t\t// existing DOM (e.g. replaceNode) we should read the existing DOM\n\t\t// attributes to diff them\n\t\tif (!isHydrating && excessDomChildren != NULL) {\n\t\t\toldProps = {};\n\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\n\t\t\t\tvalue = dom.attributes[i];\n\t\t\t\toldProps[value.name] = value.value;\n\t\t\t}\n\t\t}\n\n\t\tfor (i in oldProps) {\n\t\t\tvalue = oldProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\toldHtml = value;\n\t\t\t} else if (!(i in newProps)) {\n\t\t\t\tif (\n\t\t\t\t\t(i == 'value' && 'defaultValue' in newProps) ||\n\t\t\t\t\t(i == 'checked' && 'defaultChecked' in newProps)\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tsetProperty(dom, i, NULL, value, namespace);\n\t\t\t}\n\t\t}\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tfor (i in newProps) {\n\t\t\tvalue = newProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t\tnewChildren = value;\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\tnewHtml = value;\n\t\t\t} else if (i == 'value') {\n\t\t\t\tinputValue = value;\n\t\t\t} else if (i == 'checked') {\n\t\t\t\tchecked = value;\n\t\t\t} else if (\n\t\t\t\t(!isHydrating || typeof value == 'function') &&\n\t\t\t\toldProps[i] !== value\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, value, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\tif (\n\t\t\t\t!isHydrating &&\n\t\t\t\t(!oldHtml ||\n\t\t\t\t\t(newHtml.__html != oldHtml.__html && newHtml.__html != dom.innerHTML))\n\t\t\t) {\n\t\t\t\tdom.innerHTML = newHtml.__html;\n\t\t\t}\n\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\tif (oldHtml) dom.innerHTML = '';\n\n\t\t\tdiffChildren(\n\t\t\t\t// @ts-expect-error\n\t\t\t\tnewVNode.type == 'template' ? dom.content : dom,\n\t\t\t\tisArray(newChildren) ? newChildren : [newChildren],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnodeType == 'foreignObject' ? XHTML_NAMESPACE : namespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\texcessDomChildren\n\t\t\t\t\t? excessDomChildren[0]\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\t// Remove children that are not part of any vnode.\n\t\t\tif (excessDomChildren != NULL) {\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// As above, don't diff props during hydration\n\t\tif (!isHydrating) {\n\t\t\ti = 'value';\n\t\t\tif (nodeType == 'progress' && inputValue == NULL) {\n\t\t\t\tdom.removeAttribute('value');\n\t\t\t} else if (\n\t\t\t\tinputValue != UNDEFINED &&\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\n\t\t\t\t// despite the attribute not being present. When the attribute\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\n\t\t\t\t(inputValue !== dom[i] ||\n\t\t\t\t\t(nodeType == 'progress' && !inputValue) ||\n\t\t\t\t\t// This is only for IE 11 to fix <select> value not being updated.\n\t\t\t\t\t// To avoid a stale select value we need to set the option.value\n\t\t\t\t\t// again, which triggers IE11 to re-evaluate the select value\n\t\t\t\t\t(nodeType == 'option' && inputValue != oldProps[i]))\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, inputValue, oldProps[i], namespace);\n\t\t\t}\n\n\t\t\ti = 'checked';\n\t\t\tif (checked != UNDEFINED && checked != dom[i]) {\n\t\t\t\tsetProperty(dom, i, checked, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {Ref<any> & { _unmount?: unknown }} ref\n * @param {any} value\n * @param {VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') {\n\t\t\tlet hasRefUnmount = typeof ref._unmount == 'function';\n\t\t\tif (hasRefUnmount) {\n\t\t\t\t// @ts-ignore TS doesn't like moving narrowing checks into variables\n\t\t\t\tref._unmount();\n\t\t\t}\n\n\t\t\tif (!hasRefUnmount || value != NULL) {\n\t\t\t\t// Store the cleanup function on the function\n\t\t\t\t// instance object itself to avoid shape\n\t\t\t\t// transitioning vnode\n\t\t\t\tref._unmount = ref(value);\n\t\t\t}\n\t\t} else ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {VNode} vnode The virtual node to unmount\n * @param {VNode} parentVNode The parent of the VNode that initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current == vnode._dom) {\n\t\t\tapplyRef(r, NULL, parentVNode);\n\t\t}\n\t}\n\n\tif ((r = vnode._component) != NULL) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = NULL;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) {\n\t\t\t\tunmount(\n\t\t\t\t\tr[i],\n\t\t\t\t\tparentVNode,\n\t\t\t\t\tskipRemove || typeof vnode.type != 'function'\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!skipRemove) {\n\t\tremoveNode(vnode._dom);\n\t}\n\n\tvnode._component = vnode._parent = vnode._dom = UNDEFINED;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { EMPTY_OBJ, NULL } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\nimport { slice } from './util';\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to render into\n * @param {import('./internal').PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\t// https://github.com/preactjs/preact/issues/3794\n\tif (parentDom == document) {\n\t\tparentDom = document.documentElement;\n\t}\n\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\n\t// element..\n\tlet isHydrating = typeof replaceNode == 'function';\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? NULL\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\n\tvnode = ((!isHydrating && replaceNode) || parentDom)._children =\n\t\tcreateElement(Fragment, NULL, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [],\n\t\trefQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\tvnode,\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.namespaceURI,\n\t\t!isHydrating && replaceNode\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t\t? NULL\n\t\t\t\t: parentDom.firstChild\n\t\t\t\t\t? slice.call(parentDom.childNodes)\n\t\t\t\t\t: NULL,\n\t\tcommitQueue,\n\t\t!isHydrating && replaceNode\n\t\t\t? replaceNode\n\t\t\t: oldVNode\n\t\t\t\t? oldVNode._dom\n\t\t\t\t: parentDom.firstChild,\n\t\tisHydrating,\n\t\trefQueue\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode, refQueue);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, hydrate);\n}\n", "import { assign, slice } from './util';\nimport { createVNode } from './create-element';\nimport { NULL, UNDEFINED } from './constants';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its\n * children.\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Any additional arguments will be used\n * as replacement children.\n * @returns {import('./internal').VNode}\n */\nexport function cloneElement(vnode, props, children) {\n\tlet normalizedProps = assign({}, vnode.props),\n\t\tkey,\n\t\tref,\n\t\ti;\n\n\tlet defaultProps;\n\n\tif (vnode.type && vnode.type.defaultProps) {\n\t\tdefaultProps = vnode.type.defaultProps;\n\t}\n\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse if (props[i] === UNDEFINED && defaultProps != UNDEFINED) {\n\t\t\tnormalizedProps[i] = defaultProps[i];\n\t\t} else {\n\t\t\tnormalizedProps[i] = props[i];\n\t\t}\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tkey || vnode.key,\n\t\tref || vnode.ref,\n\t\tNULL\n\t);\n}\n", "import { NULL } from '../constants';\n\n/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {import('../internal').VNode} vnode The vnode that threw the error that was caught (except\n * for unmounting when this parameter is the highest parent that was being\n * unmounted)\n * @param {import('../internal').VNode} [oldVNode]\n * @param {import('../internal').ErrorInfo} [errorInfo]\n */\nexport function _catchError(error, vnode, oldVNode, errorInfo) {\n\t/** @type {import('../internal').Component} */\n\tlet component,\n\t\t/** @type {import('../internal').ComponentType} */\n\t\tctor,\n\t\t/** @type {boolean} */\n\t\thandled;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tctor = component.constructor;\n\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != NULL) {\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != NULL) {\n\t\t\t\t\tcomponent.componentDidCatch(error, errorInfo || {});\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n\t\t\t\tif (handled) {\n\t\t\t\t\treturn (component._pendingError = component);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n"], "names": ["slice", "options", "vnodeId", "isValidElement", "rerenderQueue", "prevDebounce", "defer", "depthSort", "CAPTURE_REGEX", "eventClock", "eventProxy", "eventProxyCapture", "i", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "isArray", "Array", "assign", "obj", "props", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "key", "ref", "normalizedProps", "arguments", "length", "call", "defaultProps", "undefined", "createVNode", "original", "vnode", "__k", "__", "__b", "__e", "__c", "constructor", "__v", "__i", "__u", "createRef", "current", "Fragment", "BaseComponent", "context", "this", "getDomSibling", "childIndex", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "__d", "push", "process", "__r", "debounceRendering", "component", "newVNode", "oldVNode", "oldDom", "commitQueue", "refQueue", "l", "sort", "shift", "__P", "diff", "__n", "namespaceURI", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "parentDom", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "namespace", "excessDomChildren", "isHydrating", "childVNode", "newDom", "firstChildDom", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructNewChildrenArray", "applyRef", "insert", "nextS<PERSON>ling", "skewedIndex", "matchingIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingOldChildren", "skew", "String", "findMatchingIndex", "unmount", "parentVNode", "contains", "insertBefore", "nodeType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "some", "x", "y", "setStyle", "style", "value", "setProperty", "test", "dom", "name", "oldValue", "useCapture", "lowerCaseName", "o", "cssText", "replace", "toLowerCase", "_attached", "addEventListener", "removeEventListener", "e", "removeAttribute", "setAttribute", "createEventProxy", "<PERSON><PERSON><PERSON><PERSON>", "_dispatched", "event", "tmp", "isNew", "oldProps", "oldState", "snapshot", "clearProcessingException", "newProps", "isClassComponent", "provider", "componentContext", "renderHook", "count", "newType", "outer", "prototype", "render", "contextType", "__E", "doR<PERSON>", "sub", "state", "__h", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "cloneNode", "then", "MODE_HYDRATE", "indexOf", "diffElementNodes", "diffed", "root", "cb", "map", "newHtml", "oldHtml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputValue", "checked", "localName", "document", "createTextNode", "createElementNS", "is", "__m", "data", "childNodes", "attributes", "__html", "innerHTML", "content", "hasRefUnmount", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "replaceNode", "documentElement", "<PERSON><PERSON><PERSON><PERSON>", "hydrate", "cloneElement", "createContext", "defaultValue", "Context", "subs", "ctx", "Set", "_props", "for<PERSON>ach", "add", "old", "delete", "Provider", "__l", "Consumer", "contextValue", "error", "errorInfo", "ctor", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "callback", "s", "forceUpdate", "Promise", "bind", "resolve", "setTimeout", "a", "b"], "mappings": "AACO,IC0BMA,EChBPC,ECPFC,EA2FSC,ECmFTC,EAWAC,EAEEC,EA0BAC,EC1MAC,EAaFC,EA+IEC,EACAC,ECzKKC,ENeEC,EAAgC,CAAG,EACnCC,EAAY,GACZC,EACZ,oECnBYC,EAAUC,MAAMD,QAStB,SAASE,EAAOC,EAAKC,GAE3B,IAAK,IAAIR,KAAKQ,EAAOD,EAAIP,GAAKQ,EAAMR,GACpC,OAA6BO,CAC9B,CAQgB,SAAAE,EAAWC,GACtBA,GAAQA,EAAKC,YAAYD,EAAKC,WAAWC,YAAYF,EAC1D,CEVgB,SAAAG,EAAcC,EAAMN,EAAOO,GAC1C,IACCC,EACAC,EACAjB,EAHGkB,EAAkB,CAAA,EAItB,IAAKlB,KAAKQ,EACA,OAALR,EAAYgB,EAAMR,EAAMR,GACd,OAALA,EAAYiB,EAAMT,EAAMR,GAC5BkB,EAAgBlB,GAAKQ,EAAMR,GAUjC,GAPImB,UAAUC,OAAS,IACtBF,EAAgBH,SACfI,UAAUC,OAAS,EAAIhC,EAAMiC,KAAKF,UAAW,GAAKJ,GAKjC,mBAARD,GHjBQ,MGiBcA,EAAKQ,aACrC,IAAKtB,KAAKc,EAAKQ,kBHjBQC,IGkBlBL,EAAgBlB,KACnBkB,EAAgBlB,GAAKc,EAAKQ,aAAatB,IAK1C,OAAOwB,EAAYV,EAAMI,EAAiBF,EAAKC,EHzB5B,KG0BpB,CAcgB,SAAAO,EAAYV,EAAMN,EAAOQ,EAAKC,EAAKQ,GAIlD,IAAMC,EAAQ,CACbZ,KAAAA,EACAN,MAAAA,EACAQ,IAAAA,EACAC,IAAAA,EACAU,IHjDkB,KGkDlBC,GHlDkB,KGmDlBC,IAAQ,EACRC,IHpDkB,KGqDlBC,IHrDkB,KGsDlBC,iBHrDuBT,EGsDvBU,IHvDkB,MGuDPR,IAAqBnC,EAAUmC,EAC1CS,KAAS,EACTC,IAAQ,GAMT,OH/DmB,MG6DfV,GH7De,MG6DKpC,EAAQqC,OAAerC,EAAQqC,MAAMA,GAEtDA,CACR,UAEgBU,IACf,MAAO,CAAEC,QHnEU,KGoEpB,CAEgB,SAAAC,EAAS9B,GACxB,OAAOA,EAAMO,QACd,CC3EO,SAASwB,EAAc/B,EAAOgC,GACpCC,KAAKjC,MAAQA,EACbiC,KAAKD,QAAUA,CAChB,UA0EgBE,EAAchB,EAAOiB,GACpC,GJ3EmB,MI2EfA,EAEH,OAAOjB,EAAKE,GACTc,EAAchB,EAAKE,GAAUF,EAAKQ,IAAU,GJ9E7B,KImFnB,IADA,IAAIU,EACGD,EAAajB,EAAKC,IAAWP,OAAQuB,IAG3C,GJtFkB,OIoFlBC,EAAUlB,EAAKC,IAAWgB,KJpFR,MIsFKC,EAAOd,IAI7B,OAAOc,EAAOd,IAShB,MAA4B,mBAAdJ,EAAMZ,KAAqB4B,EAAchB,GJnGpC,IIoGpB,CA2CA,SAASmB,EAAwBnB,GAAjC,IAGW1B,EACJ8C,EAHN,GJhJmB,OIgJdpB,EAAQA,EAAKE,KJhJC,MIgJoBF,EAAKK,IAAqB,CAEhE,IADAL,EAAKI,IAAQJ,EAAKK,IAAYgB,KJjJZ,KIkJT/C,EAAI,EAAGA,EAAI0B,EAAKC,IAAWP,OAAQpB,IAE3C,GJpJiB,OImJb8C,EAAQpB,EAAKC,IAAW3B,KJnJX,MIoJI8C,EAAKhB,IAAe,CACxCJ,EAAKI,IAAQJ,EAAKK,IAAYgB,KAAOD,EAAKhB,IAC1C,KACD,CAGD,OAAOe,EAAwBnB,EAChC,CACD,CA4BgB,SAAAsB,EAAcC,KAE1BA,EAACC,MACDD,EAACC,KAAU,IACZ1D,EAAc2D,KAAKF,KAClBG,EAAOC,OACT5D,GAAgBJ,EAAQiE,sBAExB7D,EAAeJ,EAAQiE,oBACN5D,GAAO0D,EAE1B,CASA,SAASA,IAMR,IALA,IAAIH,EAnGoBM,EAOjBC,EANHC,EACHC,EACAC,EACAC,EAgGAC,EAAI,EAIErE,EAAc4B,QAOhB5B,EAAc4B,OAASyC,GAC1BrE,EAAcsE,KAAKnE,GAGpBsD,EAAIzD,EAAcuE,QAClBF,EAAIrE,EAAc4B,OAEd6B,EAACC,MA/GCM,SALNE,GADGD,GADoBF,EAuHNN,GAtHMhB,KACNH,IACjB6B,EAAc,GACdC,EAAW,GAERL,EAASS,OACNR,EAAWlD,EAAO,GAAImD,IACpBxB,IAAawB,EAAQxB,IAAa,EACtC5C,EAAQqC,OAAOrC,EAAQqC,MAAM8B,GAEjCS,EACCV,EAASS,IACTR,EACAC,EACAF,EAASW,IACTX,EAASS,IAAYG,aJzII,GI0IzBV,EAAQtB,IAAyB,CAACuB,GJ3HjB,KI4HjBC,EJ5HiB,MI6HjBD,EAAiBhB,EAAce,GAAYC,KJ5IlB,GI6ItBD,EAAQtB,KACXyB,GAGDJ,EAAQvB,IAAawB,EAAQxB,IAC7BuB,EAAQ5B,GAAAD,IAAmB6B,EAAQtB,KAAWsB,EAC9CY,EAAWT,EAAaH,EAAUI,GAE9BJ,EAAQ1B,KAAS4B,GACpBb,EAAwBW,KA6F1BJ,EAAOC,IAAkB,CAC1B,UG3MgBgB,EACfC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAjB,EACAD,EACAmB,EACAjB,OAEI5D,EAEHyD,EAEAqB,EAEAC,EAEAC,EAiCIC,EA5BDC,EAAeT,GAAkBA,EAAc9C,KAAezB,EAE9DiF,EAAoBZ,EAAanD,OAUrC,IARAsC,EAAS0B,EACRZ,EACAD,EACAW,EACAxB,EACAyB,GAGInF,EAAI,EAAGA,EAAImF,EAAmBnF,IPhEhB,OOiElB8E,EAAaN,EAAc7C,IAAW3B,MAMrCyD,GADyB,GAAtBqB,EAAU5C,IACFjC,EAEAiF,EAAYJ,EAAU5C,MAAYjC,EAI9C6E,EAAU5C,IAAUlC,EAGhBiF,EAAShB,EACZK,EACAQ,EACArB,EACAiB,EACAC,EACAC,EACAjB,EACAD,EACAmB,EACAjB,GAIDmB,EAASD,EAAUhD,IACfgD,EAAW7D,KAAOwC,EAASxC,KAAO6D,EAAW7D,MAC5CwC,EAASxC,KACZoE,EAAS5B,EAASxC,IPjGF,KOiGa6D,GAE9BlB,EAAST,KACR2B,EAAW7D,IACX6D,EAAU/C,KAAegD,EACzBD,IPtGgB,MO0GdE,GP1Gc,MO0GWD,IAC5BC,EAAgBD,GPtHS,EO0HzBD,EAAU3C,KACVsB,EAAQ9B,MAAemD,EAAUnD,IAEjC+B,EAAS4B,EAAOR,EAAYpB,EAAQY,GACA,mBAAnBQ,EAAWhE,WPlHNS,IOkH4B0D,EAClDvB,EAASuB,EACCF,IACVrB,EAASqB,EAAOQ,aAIjBT,EAAU3C,MAAW,GAKtB,OAFAqC,EAAc1C,IAAQkD,EAEftB,CACR,CAOA,SAAS0B,EACRZ,EACAD,EACAW,EACAxB,EACAyB,GALD,IAQKnF,EAEA8E,EAEArB,EA8DG+B,EAOAC,EAnEHC,EAAoBR,EAAY9D,OACnCuE,EAAuBD,EAEpBE,EAAO,EAGX,IADApB,EAAc7C,IAAa,IAAItB,MAAM8E,GAChCnF,EAAI,EAAGA,EAAImF,EAAmBnF,IP3JhB,OO8JlB8E,EAAaP,EAAavE,KAIJ,kBAAd8E,GACc,mBAAdA,GA8CFU,EAAcxF,EAAI4F,GA/BvBd,EAAaN,EAAc7C,IAAW3B,GANjB,iBAAd8E,GACc,iBAAdA,GAEc,iBAAdA,GACPA,EAAW9C,aAAe6D,OAEiBrE,EPlL1B,KOoLhBsD,EPpLgB,gBOyLP1E,EAAQ0E,GACyBtD,EAC1Cc,EACA,CAAEvB,SAAU+D,GP5LI,gBACKvD,MOgMZuD,EAAW9C,aAA4B8C,EAAUjD,IAAU,EAK1BL,EAC1CsD,EAAWhE,KACXgE,EAAWtE,MACXsE,EAAW9D,IACX8D,EAAW7D,IAAM6D,EAAW7D,IP1MZ,KO2MhB6D,EAAU7C,KAGgC6C,GAIlClD,GAAW4C,EACrBM,EAAUjD,IAAU2C,EAAc3C,IAAU,EAY5C4B,EP/NkB,MOgOI,IARhBgC,EAAiBX,EAAU5C,IAAU4D,EAC1ChB,EACAI,EACAM,EACAG,MAMAA,KADAlC,EAAWyB,EAAYO,MAGtBhC,EAAQtB,KP7OW,IASH,MO2OCsB,GP3OD,MO2OqBA,EAAQxB,MAGxB,GAAlBwD,IAeCN,EAAoBO,EACvBE,IACUT,EAAoBO,GAC9BE,KAK4B,mBAAnBd,EAAWhE,OACrBgE,EAAU3C,KPjRc,IOmRfsD,GAAiBD,IAiBvBC,GAAiBD,EAAc,EAClCI,IACUH,GAAiBD,EAAc,EACzCI,KAEIH,EAAgBD,EACnBI,IAEAA,IAMDd,EAAU3C,KPlTc,KOgLzBqC,EAAc7C,IAAW3B,GPrKR,KOgTnB,GAAI2F,EACH,IAAK3F,EAAI,EAAGA,EAAI0F,EAAmB1F,IPjTjB,OOkTjByD,EAAWyB,EAAYlF,KACgC,IP5TnC,EO4TKyD,EAAQtB,OAC5BsB,EAAQ3B,KAAS4B,IACpBA,EAAShB,EAAce,IAGxBsC,EAAQtC,EAAUA,IAKrB,OAAOC,CACR,CAQA,SAAS4B,EAAOU,EAAatC,EAAQY,GAArC,IAIMvD,EACKf,EAFV,GAA+B,mBAApBgG,EAAYlF,KAAoB,CAE1C,IADIC,EAAWiF,EAAWrE,IACjB3B,EAAI,EAAGe,GAAYf,EAAIe,EAASK,OAAQpB,IAC5Ce,EAASf,KAKZe,EAASf,GAAE4B,GAAWoE,EACtBtC,EAAS4B,EAAOvE,EAASf,GAAI0D,EAAQY,IAIvC,OAAOZ,CACR,CAAWsC,EAAWlE,KAAS4B,IAC1BA,GAAUsC,EAAYlF,OAASwD,EAAU2B,SAASvC,KACrDA,EAAShB,EAAcsD,IAExB1B,EAAU4B,aAAaF,EAAWlE,IAAO4B,GP3VvB,MO4VlBA,EAASsC,EAAWlE,KAGrB,GACC4B,EAASA,GAAUA,EAAO6B,kBPhWR,MOiWV7B,GAAqC,GAAnBA,EAAOyC,UAElC,OAAOzC,CACR,UAQgB0C,EAAarF,EAAUsF,GAUtC,OATAA,EAAMA,GAAO,GP7WM,MO8WftF,GAAuC,kBAAZA,IACpBX,EAAQW,GAClBA,EAASuF,KAAK,SAAAxD,GACbsD,EAAatD,EAAOuD,EACrB,GAEAA,EAAIlD,KAAKpC,IAEHsF,CACR,CASA,SAASP,EACRhB,EACAI,EACAM,EACAG,GAJD,IAmCMY,EACAC,EA9BCxF,EAAM8D,EAAW9D,IACjBF,EAAOgE,EAAWhE,KACpB2C,EAAWyB,EAAYM,GAkB3B,GP1ZmB,OO2ZjB/B,GAAuC,MAAlBqB,EAAW9D,KAChCyC,GACAzC,GAAOyC,EAASzC,KAChBF,GAAQ2C,EAAS3C,MACc,IPxaX,EOwanB2C,EAAQtB,KAEV,OAAOqD,KAVPG,GPvZkB,MOwZjBlC,GAAmD,IPja/B,EOiaCA,EAAQtB,KAA0B,EAAI,GAa5D,IAFIoE,EAAIf,EAAc,EAClBgB,EAAIhB,EAAc,EACfe,GAAK,GAAKC,EAAItB,EAAY9D,QAAQ,CACxC,GAAImF,GAAK,EAAG,CAEX,IADA9C,EAAWyB,EAAYqB,KAGS,IPnbb,EOmbjB9C,EAAQtB,MACTnB,GAAOyC,EAASzC,KAChBF,GAAQ2C,EAAS3C,KAEjB,OAAOyF,EAERA,GACD,CAEA,GAAIC,EAAItB,EAAY9D,OAAQ,CAE3B,IADAqC,EAAWyB,EAAYsB,KAGS,IPhcb,EOgcjB/C,EAAQtB,MACTnB,GAAOyC,EAASzC,KAChBF,GAAQ2C,EAAS3C,KAEjB,OAAO0F,EAERA,GACD,CACD,CAGD,OAAQ,CACT,CFhdA,SAASC,EAASC,EAAO1F,EAAK2F,GACf,KAAV3F,EAAI,GACP0F,EAAME,YAAY5F,ELWA,MKXK2F,EAAgB,GAAKA,GAE5CD,EAAM1F,GLSY,MKVR2F,EACG,GACa,iBAATA,GAAqBxG,EAAmB0G,KAAK7F,GACjD2F,EAEAA,EAAQ,IAEvB,CAyBgB,SAAAC,EAAYE,EAAKC,EAAMJ,EAAOK,EAAUrC,GAAxC,IACXsC,EA8BGC,EA5BPC,EAAG,GAAY,SAARJ,EACN,GAAoB,iBAATJ,EACVG,EAAIJ,MAAMU,QAAUT,MACd,CAKN,GAJuB,iBAAZK,IACVF,EAAIJ,MAAMU,QAAUJ,EAAW,IAG5BA,EACH,IAAKD,KAAQC,EACNL,GAASI,KAAQJ,GACtBF,EAASK,EAAIJ,MAAOK,EAAM,IAK7B,GAAIJ,EACH,IAAKI,KAAQJ,EACPK,GAAYL,EAAMI,IAASC,EAASD,IACxCN,EAASK,EAAIJ,MAAOK,EAAMJ,EAAMI,GAIpC,MAGI,GAAe,KAAXA,EAAK,IAAwB,KAAXA,EAAK,GAC/BE,EAAaF,IAASA,EAAOA,EAAKM,QAAQzH,EAAe,OACnDsH,EAAgBH,EAAKO,cAI1BP,EADGG,KAAiBJ,GAAe,cAARC,GAAgC,aAARA,EAC5CG,EAAc9H,MAAM,GAChB2H,EAAK3H,MAAM,GAElB0H,EAAGjD,IAAaiD,EAAGjD,EAAc,CAAE,GACxCiD,EAAGjD,EAAYkD,EAAOE,GAAcN,EAEhCA,EACEK,EAQJL,EAAMY,EAAYP,EAASO,GAP3BZ,EAAMY,EAAY1H,EAClBiH,EAAIU,iBACHT,EACAE,EAAalH,EAAoBD,EACjCmH,IAMFH,EAAIW,oBACHV,EACAE,EAAalH,EAAoBD,EACjCmH,OAGI,CACN,GLtF2B,8BKsFvBtC,EAIHoC,EAAOA,EAAKM,QAAQ,cAAe,KAAKA,QAAQ,SAAU,UAE1DN,GAAQ,SAARA,GACQ,UAARA,GACQ,QAARA,GACQ,QAARA,GACQ,QAARA,GAGQ,YAARA,GACQ,YAARA,GACQ,WAARA,GACQ,WAARA,GACQ,QAARA,GACQ,WAARA,GACAA,KAAQD,EAER,IACCA,EAAIC,GLxGY,MKwGJJ,EAAgB,GAAKA,EAEjC,MAAMQ,CAER,CADG,MAAOO,GACV,CASoB,mBAATf,ILrHO,MKuHPA,IAA4B,IAAVA,GAA8B,KAAXI,EAAK,GAGpDD,EAAIa,gBAAgBZ,GAFpBD,EAAIc,aAAab,EAAc,WAARA,GAA8B,GAATJ,EAAgB,GAAKA,GAInE,CACD,CAOA,SAASkB,EAAiBZ,GAMzB,gBAAiBS,GAChB,GAAIjF,KAAIoB,EAAa,CACpB,IAAMiE,EAAerF,KAAIoB,EAAY6D,EAAE5G,KAAOmG,GAC9C,GL7IiB,MK6IbS,EAAEK,EACLL,EAAEK,EAAclI,SAKV,GAAI6H,EAAEK,EAAcD,EAAaP,EACvC,OAED,OAAOO,EAAazI,EAAQ2I,MAAQ3I,EAAQ2I,MAAMN,GAAKA,EACxD,CACD,CACD,UGzHgBzD,EACfK,EACAd,EACAC,EACAiB,EACAC,EACAC,EACAjB,EACAD,EACAmB,EACAjB,OAGIqE,EAkBEhF,EAAGiF,EAAOC,EAAUC,EAAUC,EAAUC,EACxCC,EACEC,EAMFC,EACAC,EAyGO1I,EA4BP2I,EACHC,EASS5I,EA6BNuE,EAgDOvE,EAtPZ6I,EAAUrF,EAAS1C,KAIpB,GRjDwBS,MQiDpBiC,EAASxB,YAA0B,ORlDpB,KAbU,IQkEzByB,EAAQtB,MACX0C,KRrE0B,GQqETpB,EAAQtB,KAEzByC,EAAoB,CADpBlB,EAASF,EAAQ1B,IAAQ2B,EAAQ3B,OAI7BmG,EAAM5I,EAAOwC,MAASoG,EAAIzE,GAE/BsF,EAAO,GAAsB,mBAAXD,EACjB,IAkEC,GAhEIN,EAAW/E,EAAShD,MAClBgI,EACL,cAAeK,GAAWA,EAAQE,UAAUC,OAKzCP,GADJR,EAAMY,EAAQI,cACQvE,EAAcuD,EAAGlG,KACnC2G,EAAmBT,EACpBQ,EACCA,EAASjI,MAAMmG,MACfsB,EAAGrG,GACJ8C,EAGCjB,EAAQ1B,IAEXuG,GADArF,EAAIO,EAAQzB,IAAc0B,EAAQ1B,KACNH,GAAwBqB,EAACiG,KAGjDV,EAEHhF,EAAQzB,IAAckB,EAAI,IAAI4F,EAAQN,EAAUG,IAGhDlF,EAAQzB,IAAckB,EAAI,IAAIV,EAC7BgG,EACAG,GAEDzF,EAAEjB,YAAc6G,EAChB5F,EAAE+F,OAASG,GAERV,GAAUA,EAASW,IAAInG,GAE3BA,EAAEzC,MAAQ+H,EACLtF,EAAEoG,QAAOpG,EAAEoG,MAAQ,CAAA,GACxBpG,EAAET,QAAUkG,EACZzF,EAACiB,IAAkBQ,EACnBwD,EAAQjF,EAACC,KAAU,EACnBD,EAACqG,IAAoB,GACrBrG,EAACsG,IAAmB,IAIjBf,GR5Ga,MQ4GOvF,EAACuG,MACxBvG,EAACuG,IAAcvG,EAAEoG,OAGdb,GRhHa,MQgHOK,EAAQY,2BAC3BxG,EAACuG,KAAevG,EAAEoG,QACrBpG,EAACuG,IAAclJ,EAAO,GAAI2C,EAACuG,MAG5BlJ,EACC2C,EAACuG,IACDX,EAAQY,yBAAyBlB,EAAUtF,EAACuG,OAI9CrB,EAAWlF,EAAEzC,MACb4H,EAAWnF,EAAEoG,MACbpG,EAAChB,IAAUuB,EAGP0E,EAEFM,GRlIe,MQmIfK,EAAQY,0BRnIO,MQoIfxG,EAAEyG,oBAEFzG,EAAEyG,qBAGClB,GRzIY,MQyIQvF,EAAE0G,mBACzB1G,EAACqG,IAAkBnG,KAAKF,EAAE0G,uBAErB,CAUN,GARCnB,GR9Ie,MQ+IfK,EAAQY,0BACRlB,IAAaJ,GRhJE,MQiJflF,EAAE2G,2BAEF3G,EAAE2G,0BAA0BrB,EAAUG,IAIpCzF,EAACnB,KRvJY,MQwJdmB,EAAE4G,wBAKI,IAJN5G,EAAE4G,sBACDtB,EACAtF,EAACuG,IACDd,IAEFlF,EAAQvB,KAAcwB,EAAQxB,IAC7B,CAkBD,IAhBIuB,EAAQvB,KAAcwB,EAAQxB,MAKjCgB,EAAEzC,MAAQ+H,EACVtF,EAAEoG,MAAQpG,EAACuG,IACXvG,EAACC,KAAU,GAGZM,EAAQ1B,IAAQ2B,EAAQ3B,IACxB0B,EAAQ7B,IAAa8B,EAAQ9B,IAC7B6B,EAAQ7B,IAAW2E,KAAK,SAAA5E,GACnBA,IAAOA,EAAKE,GAAW4B,EAC5B,GAESxD,EAAI,EAAGA,EAAIiD,EAACsG,IAAiBnI,OAAQpB,IAC7CiD,EAACqG,IAAkBnG,KAAKF,EAACsG,IAAiBvJ,IAE3CiD,EAACsG,IAAmB,GAEhBtG,EAACqG,IAAkBlI,QACtBuC,EAAYR,KAAKF,GAGlB,MAAM6F,CACP,CR3LgB,MQ6LZ7F,EAAE6G,qBACL7G,EAAE6G,oBAAoBvB,EAAUtF,EAACuG,IAAad,GAG3CF,GRjMY,MQiMQvF,EAAE8G,oBACzB9G,EAACqG,IAAkBnG,KAAK,WACvBF,EAAE8G,mBAAmB5B,EAAUC,EAAUC,EAC1C,EAEF,CASA,GAPApF,EAAET,QAAUkG,EACZzF,EAAEzC,MAAQ+H,EACVtF,EAACe,IAAcM,EACfrB,EAACnB,KAAU,EAEP6G,EAAatJ,EAAOgE,IACvBuF,EAAQ,EACLJ,EAAkB,CAQrB,IAPAvF,EAAEoG,MAAQpG,EAACuG,IACXvG,EAACC,KAAU,EAEPyF,GAAYA,EAAWnF,GAE3ByE,EAAMhF,EAAE+F,OAAO/F,EAAEzC,MAAOyC,EAAEoG,MAAOpG,EAAET,SAE1BxC,EAAI,EAAGA,EAAIiD,EAACsG,IAAiBnI,OAAQpB,IAC7CiD,EAACqG,IAAkBnG,KAAKF,EAACsG,IAAiBvJ,IAE3CiD,EAACsG,IAAmB,EACrB,MACC,GACCtG,EAACC,KAAU,EACPyF,GAAYA,EAAWnF,GAE3ByE,EAAMhF,EAAE+F,OAAO/F,EAAEzC,MAAOyC,EAAEoG,MAAOpG,EAAET,SAGnCS,EAAEoG,MAAQpG,EAACuG,UACHvG,EAACC,OAAa0F,EAAQ,IAIhC3F,EAAEoG,MAAQpG,EAACuG,IRxOM,MQ0ObvG,EAAE+G,kBACLtF,EAAgBpE,EAAOA,EAAO,CAAE,EAAEoE,GAAgBzB,EAAE+G,oBAGjDxB,IAAqBN,GR9OR,MQ8OiBjF,EAAEgH,0BACnC5B,EAAWpF,EAAEgH,wBAAwB9B,EAAUC,IAK5C7D,EAAe0D,ERpPF,MQmPhBA,GAAeA,EAAInH,OAASwB,GRnPZ,MQmPwB2F,EAAIjH,MAI5CuD,EAAe2F,EAAUjC,EAAIzH,MAAMO,WAGpC2C,EAASW,EACRC,EACAlE,EAAQmE,GAAgBA,EAAe,CAACA,GACxCf,EACAC,EACAiB,EACAC,EACAC,EACAjB,EACAD,EACAmB,EACAjB,GAGDX,EAAEF,KAAOS,EAAQ1B,IAGjB0B,EAAQrB,MRjRe,IQmRnBc,EAACqG,IAAkBlI,QACtBuC,EAAYR,KAAKF,GAGdqF,IACHrF,EAACiG,IAAiBjG,EAACrB,GRlRH,KQ6SlB,CAzBE,MAAO8F,GAGR,GAFAlE,EAAQvB,IRrRS,KQuRb4C,GRvRa,MQuRED,EAClB,GAAI8C,EAAEyC,KAAM,CAKX,IAJA3G,EAAQrB,KAAW0C,EAChBuF,IRvSsB,IQ0SlB1G,GAA6B,GAAnBA,EAAOyC,UAAiBzC,EAAO6B,aAC/C7B,EAASA,EAAO6B,YAGjBX,EAAkBA,EAAkByF,QAAQ3G,IRjS7B,KQkSfF,EAAQ1B,IAAQ4B,CACjB,MACC,IAAS1D,EAAI4E,EAAkBxD,OAAQpB,KACtCS,EAAWmE,EAAkB5E,SAI/BwD,EAAQ1B,IAAQ2B,EAAQ3B,IACxB0B,EAAQ7B,IAAa8B,EAAQ9B,IAE9BtC,EAAOyC,IAAa4F,EAAGlE,EAAUC,EAClC,MR7SkB,MQ+SlBmB,GACApB,EAAQvB,KAAcwB,EAAQxB,KAE9BuB,EAAQ7B,IAAa8B,EAAQ9B,IAC7B6B,EAAQ1B,IAAQ2B,EAAQ3B,KAExB4B,EAASF,EAAQ1B,IAAQwI,EACxB7G,EAAQ3B,IACR0B,EACAC,EACAiB,EACAC,EACAC,EACAjB,EACAkB,EACAjB,GAMF,OAFKqE,EAAM5I,EAAQkL,SAAStC,EAAIzE,GR/UH,IQiVtBA,EAAQrB,SAA2BZ,EAAYmC,CACvD,UAOgBU,EAAWT,EAAa6G,EAAM5G,GAC7C,IAAK,IAAI5D,EAAI,EAAGA,EAAI4D,EAASxC,OAAQpB,IACpCqF,EAASzB,EAAS5D,GAAI4D,IAAW5D,GAAI4D,IAAW5D,IAG7CX,EAAO0C,KAAU1C,EAAO0C,IAASyI,EAAM7G,GAE3CA,EAAY2C,KAAK,SAAArD,GAChB,IAECU,EAAcV,EAACqG,IACfrG,EAACqG,IAAoB,GACrB3F,EAAY2C,KAAK,SAAAmE,GAEhBA,EAAGpJ,KAAK4B,EACT,EAGD,CAFE,MAAOyE,GACRrI,EAAOyC,IAAa4F,EAAGzE,EAAChB,IACzB,CACD,EACD,CAEA,SAASiI,EAAUxJ,GAClB,MACgB,iBAARA,GRpWW,MQqWlBA,GACCA,EAAImB,KAAWnB,EAAImB,IAAU,EAEvBnB,EAGJN,EAAQM,GACJA,EAAKgK,IAAIR,GAGV5J,EAAO,CAAE,EAAEI,EACnB,CAiBA,SAAS4J,EACRxD,EACAtD,EACAC,EACAiB,EACAC,EACAC,EACAjB,EACAkB,EACAjB,GATD,IAeK5D,EAEA2K,EAEAC,EAEAC,EACAlE,EACAmE,EACAC,EAbA5C,EAAW1E,EAASjD,MACpB+H,EAAW/E,EAAShD,MACpB2F,EAAkC3C,EAAS1C,KAkB/C,GAJgB,OAAZqF,EAAmBxB,ERhaK,6BQiaP,QAAZwB,EAAoBxB,ER/ZA,qCQganBA,IAAWA,ERjaS,gCAGX,MQgafC,EACH,IAAK5E,EAAI,EAAGA,EAAI4E,EAAkBxD,OAAQpB,IAMzC,IALA2G,EAAQ/B,EAAkB5E,KAOzB,iBAAkB2G,KAAWR,IAC5BA,EAAWQ,EAAMqE,WAAa7E,EAA6B,GAAlBQ,EAAMR,UAC/C,CACDW,EAAMH,EACN/B,EAAkB5E,GR7aF,KQ8ahB,KACD,CAIF,GRnbmB,MQmbf8G,EAAa,CAChB,GRpbkB,MQobdX,EACH,OAAO8E,SAASC,eAAe3C,GAGhCzB,EAAMmE,SAASE,gBACdxG,EACAwB,EACAoC,EAAS6C,IAAM7C,GAKZ1D,IACCxF,EAAOgM,KACVhM,EAAOgM,IAAoB7H,EAAUoB,GACtCC,GAAc,GAGfD,ERtckB,IQucnB,CAEA,GRzcmB,MQycfuB,EAECgC,IAAaI,GAAc1D,GAAeiC,EAAIwE,MAAQ/C,IACzDzB,EAAIwE,KAAO/C,OAEN,CASN,GAPA3D,EAAoBA,GAAqBxF,EAAMiC,KAAKyF,EAAIyE,YAExDpD,EAAW1E,EAASjD,OAASP,GAKxB4E,GRvda,MQudED,EAEnB,IADAuD,EAAW,CAAA,EACNnI,EAAI,EAAGA,EAAI8G,EAAI0E,WAAWpK,OAAQpB,IAEtCmI,GADAxB,EAAQG,EAAI0E,WAAWxL,IACR+G,MAAQJ,EAAMA,MAI/B,IAAK3G,KAAKmI,EAET,GADAxB,EAAQwB,EAASnI,GACR,YAALA,QACOA,GAAK,2BAALA,EACV4K,EAAUjE,OACJ,KAAM3G,KAAKuI,GAAW,CAC5B,GACO,SAALvI,GAAgB,iBAAkBuI,GAC7B,WAALvI,GAAkB,mBAAoBuI,EAEvC,SAED3B,EAAYE,EAAK9G,ER3eD,KQ2eU2G,EAAOhC,EAClC,CAKD,IAAK3E,KAAKuI,EACT5B,EAAQ4B,EAASvI,GACR,YAALA,EACH6K,EAAclE,EACC,2BAAL3G,EACV2K,EAAUhE,EACK,SAAL3G,EACV8K,EAAanE,EACE,WAAL3G,EACV+K,EAAUpE,EAER9B,GAA+B,mBAAT8B,GACxBwB,EAASnI,KAAO2G,GAEhBC,EAAYE,EAAK9G,EAAG2G,EAAOwB,EAASnI,GAAI2E,GAK1C,GAAIgG,EAGD9F,GACC+F,IACAD,EAAOc,QAAWb,EAAOa,QAAWd,EAAOc,QAAW3E,EAAI4E,aAE5D5E,EAAI4E,UAAYf,EAAOc,QAGxBjI,EAAQ7B,IAAa,QAsBrB,GApBIiJ,IAAS9D,EAAI4E,UAAY,IAE7BrH,EAEkB,YAAjBb,EAAS1C,KAAqBgG,EAAI6E,QAAU7E,EAC5C1G,EAAQyK,GAAeA,EAAc,CAACA,GACtCrH,EACAC,EACAiB,EACY,iBAAZyB,ER5hB2B,+BQ4hBqBxB,EAChDC,EACAjB,EACAiB,EACGA,EAAkB,GAClBnB,EAAQ9B,KAAce,EAAce,EAAU,GACjDoB,EACAjB,GRhiBgB,MQoiBbgB,EACH,IAAK5E,EAAI4E,EAAkBxD,OAAQpB,KAClCS,EAAWmE,EAAkB5E,IAM3B6E,IACJ7E,EAAI,QACY,YAAZmG,GR9iBa,MQ8iBa2E,EAC7BhE,EAAIa,gBAAgB,SR9iBCpG,MQgjBrBuJ,IAKCA,IAAehE,EAAI9G,IACN,YAAZmG,IAA2B2E,GAIf,UAAZ3E,GAAwB2E,GAAc3C,EAASnI,KAEjD4G,EAAYE,EAAK9G,EAAG8K,EAAY3C,EAASnI,GAAI2E,GAG9C3E,EAAI,UR/jBkBuB,MQgkBlBwJ,GAAwBA,GAAWjE,EAAI9G,IAC1C4G,EAAYE,EAAK9G,EAAG+K,EAAS5C,EAASnI,GAAI2E,GAG7C,CAEA,OAAOmC,CACR,CAQgB,SAAAzB,EAASpE,EAAK0F,EAAOjF,GACpC,IACC,GAAkB,mBAAPT,EAAmB,CAC7B,IAAI2K,EAAuC,mBAAhB3K,EAAGkB,IAC1ByJ,GAEH3K,EAAGkB,MAGCyJ,GRzlBY,MQylBKjF,IAIrB1F,EAAGkB,IAAYlB,EAAI0F,GAErB,MAAO1F,EAAIoB,QAAUsE,CAGtB,CAFE,MAAOe,GACRrI,EAAOyC,IAAa4F,EAAGhG,EACxB,CACD,CASgB,SAAAqE,EAAQrE,EAAOsE,EAAa6F,GAA5B,IACXC,EAsBM9L,EAbV,GARIX,EAAQ0G,SAAS1G,EAAQ0G,QAAQrE,IAEhCoK,EAAIpK,EAAMT,OACT6K,EAAEzJ,SAAWyJ,EAAEzJ,SAAWX,EAAKI,KACnCuD,EAASyG,ERlnBQ,KQknBC9F,IRlnBD,OQsnBd8F,EAAIpK,EAAKK,KAAsB,CACnC,GAAI+J,EAAEC,qBACL,IACCD,EAAEC,sBAGH,CAFE,MAAOrE,GACRrI,EAAOyC,IAAa4F,EAAG1B,EACxB,CAGD8F,EAAE/I,KAAO+I,EAAC9H,IR/nBQ,IQgoBnB,CAEA,GAAK8H,EAAIpK,EAAKC,IACb,IAAS3B,EAAI,EAAGA,EAAI8L,EAAE1K,OAAQpB,IACzB8L,EAAE9L,IACL+F,EACC+F,EAAE9L,GACFgG,EACA6F,GAAmC,mBAAdnK,EAAMZ,MAM1B+K,GACJpL,EAAWiB,EAAKI,KAGjBJ,EAAKK,IAAcL,EAAKE,GAAWF,EAAKI,SRjpBhBP,CQkpBzB,CAGA,SAAS4H,EAAS3I,EAAO6I,EAAO7G,GAC/B,YAAYR,YAAYxB,EAAOgC,EAChC,CC3pBO,SAASwG,EAAOtH,EAAO4C,EAAW0H,GAAlC,IAWFnH,EAOApB,EAQAE,EACHC,EAzBGU,GAAa2G,WAChB3G,EAAY2G,SAASgB,iBAGlB5M,EAAOuC,IAAQvC,EAAOuC,GAAOF,EAAO4C,GAYpCb,GAPAoB,EAAoC,mBAAfmH,GTRN,KSiBfA,GAAeA,EAAWrK,KAAe2C,EAAS3C,IAMlDgC,EAAc,GACjBC,EAAW,GACZK,EACCK,EAPD5C,IAAWmD,GAAemH,GAAgB1H,GAAS3C,IAClDd,EAAcyB,ETpBI,KSoBY,CAACZ,IAU/B+B,GAAYxD,EACZA,EACAqE,EAAUH,cACTU,GAAemH,EACb,CAACA,GACDvI,ETnCe,KSqCda,EAAU4H,WACT9M,EAAMiC,KAAKiD,EAAUiH,YTtCR,KSwClB5H,GACCkB,GAAemH,EACbA,EACAvI,EACCA,EAAQ3B,IACRwC,EAAU4H,WACdrH,EACAjB,GAIDQ,EAAWT,EAAajC,EAAOkC,EAChC,CAOO,SAASuI,EAAQzK,EAAO4C,GAC9B0E,EAAOtH,EAAO4C,EAAW6H,EAC1B,UChEgBC,EAAa1K,EAAOlB,EAAOO,OAEzCC,EACAC,EACAjB,EAEGsB,EALAJ,EAAkBZ,EAAO,CAAE,EAAEoB,EAAMlB,OAWvC,IAAKR,KAJD0B,EAAMZ,MAAQY,EAAMZ,KAAKQ,eAC5BA,EAAeI,EAAMZ,KAAKQ,cAGjBd,EACA,OAALR,EAAYgB,EAAMR,EAAMR,GACd,OAALA,EAAYiB,EAAMT,EAAMR,GAEhCkB,EAAgBlB,QVZMuB,IUWdf,EAAMR,IVXQuB,MUWYD,EACbA,EAAatB,GAEbQ,EAAMR,GAS7B,OALImB,UAAUC,OAAS,IACtBF,EAAgBH,SACfI,UAAUC,OAAS,EAAIhC,EAAMiC,KAAKF,UAAW,GAAKJ,GAG7CS,EACNE,EAAMZ,KACNI,EACAF,GAAOU,EAAMV,IACbC,GAAOS,EAAMT,IV5BK,KU+BpB,CJ1CgB,SAAAoL,EAAcC,GAC7B,SAASC,EAAQ/L,GAAjB,IAGMgM,EACAC,EA+BL,OAlCKhK,KAAKuH,kBAELwC,EAAO,IAAIE,KACXD,EAAM,CAAE,GACRF,EAAOxK,KAAQU,KAEnBA,KAAKuH,gBAAkB,WAAM,OAAAyC,CAAG,EAEhChK,KAAKsJ,qBAAuB,WAC3BS,ENAgB,IMCjB,EAEA/J,KAAKoH,sBAAwB,SAAU8C,GAElClK,KAAKjC,MAAMmG,OAASgG,EAAOhG,OAC9B6F,EAAKI,QAAQ,SAAA3J,GACZA,EAACnB,KAAU,EACXkB,EAAcC,EACf,EAEF,EAEAR,KAAK2G,IAAM,SAAAnG,GACVuJ,EAAKK,IAAI5J,GACT,IAAI6J,EAAM7J,EAAE8I,qBACZ9I,EAAE8I,qBAAuB,WACpBS,GACHA,EAAKO,OAAO9J,GAET6J,GAAKA,EAAIzL,KAAK4B,EACnB,CACD,GAGMzC,EAAMO,QACd,CAgBA,OAdAwL,EAAOxK,IAAO,OAAS/B,IACvBuM,EAAO3K,GAAiB0K,EAQxBC,EAAQS,SACPT,EAAOU,KANRV,EAAQW,SAAW,SAAC1M,EAAO2M,GAC1B,OAAO3M,EAAMO,SAASoM,EACvB,GAKkBlE,YAChBsD,EAEKA,CACR,CLhCanN,EAAQc,EAAUd,MChBzBC,EAAU,CACfyC,ISDM,SAAqBsL,EAAO1L,EAAO+B,EAAU4J,GAQnD,IANA,IAAI9J,EAEH+J,EAEAC,EAEO7L,EAAQA,EAAKE,IACpB,IAAK2B,EAAY7B,EAAKK,OAAiBwB,EAAS3B,GAC/C,IAcC,IAbA0L,EAAO/J,EAAUvB,cXND,MWQJsL,EAAKE,2BAChBjK,EAAUkK,SAASH,EAAKE,yBAAyBJ,IACjDG,EAAUhK,EAASL,KXVJ,MWaZK,EAAUmK,oBACbnK,EAAUmK,kBAAkBN,EAAOC,GAAa,CAAE,GAClDE,EAAUhK,EAASL,KAIhBqK,EACH,OAAQhK,EAAS2F,IAAiB3F,CAIpC,CAFE,MAAOmE,GACR0F,EAAQ1F,CACT,CAIF,MAAM0F,CACP,GRzCI9N,EAAU,EA2FDC,EAAiB,SAAAmC,GAAK,OH/Ef,MGgFnBA,GH/EwBH,MG+EPG,EAAMM,WAAwB,ECrEhDO,EAAcwG,UAAU0E,SAAW,SAAUE,EAAQC,GAEpD,IAAIC,EAEHA,EJfkB,MIcfpL,KAAI+G,KAAuB/G,KAAI+G,KAAe/G,KAAK4G,MAClD5G,KAAI+G,IAEJ/G,KAAI+G,IAAclJ,EAAO,CAAE,EAAEmC,KAAK4G,OAGlB,mBAAVsE,IAGVA,EAASA,EAAOrN,EAAO,GAAIuN,GAAIpL,KAAKjC,QAGjCmN,GACHrN,EAAOuN,EAAGF,GJ3BQ,MI+BfA,GAEAlL,KAAIR,MACH2L,GACHnL,KAAI8G,IAAiBpG,KAAKyK,GAE3B5K,EAAcP,MAEhB,EAQAF,EAAcwG,UAAU+E,YAAc,SAAUF,GAC3CnL,KAAIR,MAIPQ,KAAIX,KAAU,EACV8L,GAAUnL,KAAI6G,IAAkBnG,KAAKyK,GACzC5K,EAAcP,MAEhB,EAYAF,EAAcwG,UAAUC,OAAS1G,EA8F7B9C,EAAgB,GAadE,EACa,mBAAXqO,QACJA,QAAQhF,UAAUoB,KAAK6D,KAAKD,QAAQE,WACpCC,WAuBEvO,EAAY,SAACwO,EAAGC,UAAMD,EAAClM,IAAAJ,IAAiBuM,EAACnM,IAAAJ,GAAc,EA8B7DuB,EAAOC,IAAkB,ECxOnBzD,EAAgB,8BAalBC,EAAa,EA+IXC,EAAa+H,GAAiB,GAC9B9H,EAAoB8H,GAAiB,GCzKhC7H,EAAI"}