import type { Metadata } from 'next'
import { Inter, Poppins } from 'next/font/google'
import './globals.css'
import { config } from '@/lib/config'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const poppins = Poppins({ 
  subsets: ['latin'],
  weight: ['400', '500', '600', '700', '800'],
  variable: '--font-poppins',
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: config.app.name,
    template: `%s | ${config.app.name}`,
  },
  description: config.app.tagline + ' - Fresh mangoes delivered to your doorstep with AI-powered automation.',
  keywords: ['mangoes', 'fresh fruit', 'delivery', 'Pakistan', 'AI', 'automation'],
  authors: [{ name: 'MangoEase AI Team' }],
  creator: 'MangoEase AI',
  publisher: 'MangoEase AI',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXTAUTH_URL || 'http://localhost:3000'),
  openGraph: {
    type: 'website',
    locale: 'en_PK',
    url: '/',
    title: config.app.name,
    description: config.app.tagline,
    siteName: config.app.name,
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: config.app.name,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: config.app.name,
    description: config.app.tagline,
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#FFC107" />
        <meta name="msapplication-TileColor" content="#FFC107" />
      </head>
      <body className={`${inter.className} antialiased bg-cream min-h-screen`}>
        <div id="root">
          {children}
        </div>
        
        {/* Global Loading Overlay */}
        <div id="loading-overlay" className="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div className="spinner"></div>
            <span className="text-gray-700 font-medium">Processing...</span>
          </div>
        </div>
        
        {/* Toast Container */}
        <div id="toast-container" className="fixed top-4 right-4 z-50 space-y-2"></div>
      </body>
    </html>
  )
}
