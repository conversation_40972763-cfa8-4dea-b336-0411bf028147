#!/bin/bash

echo "🥭 Setting up MangoEase AI..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Check if .env exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration before running the app"
fi

# Generate Prisma client
echo "🗄️  Setting up database..."
npx prisma generate

echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your database and API keys"
echo "2. Run 'npx prisma db push' to create database tables"
echo "3. Run 'npm run dev' to start the development server"
echo ""
echo "Visit http://localhost:3000 to see your MangoEase AI platform!"
