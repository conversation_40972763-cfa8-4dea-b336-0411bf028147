# 🥭 MangoEase AI - Automated Mango Delivery Platform

**Harvest to Home, Hassle-Free** - A fully automated, AI-powered web platform for selling and delivering fresh mangoes across Pakistan.

## 🌟 Features

### 🤖 AI-Powered Automation
- **Order Intake Agent**: Processes customer orders with natural language understanding
- **Invoice Agent**: Automatically generates PDF invoices
- **Payment Verification Agent**: OCR-based payment screenshot verification
- **Delivery Agent**: Books courier services automatically
- **Tracking Agent**: Real-time delivery status updates
- **Notification Agent**: Automated customer communications

### 🎨 Modern UI/UX
- **Tropical Minimalism Design**: Mango-inspired color palette
- **Responsive Design**: Works perfectly on mobile and desktop
- **Smooth Animations**: Framer Motion powered interactions
- **Real-time Updates**: Live order tracking and status updates

### 🔧 Technical Stack
- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL (Supabase ready)
- **AI**: <PERSON><PERSON><PERSON><PERSON>, OpenAI GPT-4
- **OCR**: Tesseract.js for payment verification
- **PDF**: jsPDF for invoice generation
- **Animations**: Framer Motion

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL database (or Supabase account)
- OpenAI API key

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd mangoease-ai
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/mangoease"

# Supabase (alternative to local PostgreSQL)
SUPABASE_URL="your-supabase-url"
SUPABASE_ANON_KEY="your-supabase-anon-key"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"

# Business Settings
MANGO_PRICE_PER_KG=150
DELIVERY_CHARGE_KARACHI=200
DELIVERY_CHARGE_LAHORE=250
DELIVERY_CHARGE_ISLAMABAD=300
```

4. **Set up the database**
```bash
npx prisma db push
npx prisma generate
```

5. **Run the development server**
```bash
npm run dev
```

6. **Open your browser**
Navigate to `http://localhost:3000`

## 📱 Usage

### For Customers
1. **Place Order**: Visit `/order` and specify quantity and delivery details
2. **AI Processing**: Our AI agents automatically process your order
3. **Payment**: Upload payment screenshot for verification
4. **Track Order**: Use order number to track delivery status

### For Admin
1. **Dashboard**: Visit `/admin` to monitor all orders
2. **Real-time Stats**: View revenue, order counts, and trends
3. **Agent Logs**: Monitor AI agent activities and decisions
4. **Manual Override**: Manually verify payments when needed

## 🤖 AI Agent Workflows

### 1. Order Intake Agent
```typescript
// Processes natural language orders
"I want 5kg mangoes delivered to Karachi"
↓
// Extracts: quantity=5, city=Karachi
// Calculates: pricing, delivery charges
// Creates: Order record in database
```

### 2. Invoice Agent
```typescript
// Generates professional PDF invoices
Order Created → Generate Invoice Number → Create PDF → Store URL
```

### 3. Payment Verification Agent
```typescript
// OCR-based payment verification
Screenshot Upload → Tesseract OCR → Extract Amount/Transaction ID → Auto-verify or Flag for Review
```

### 4. Delivery Agent
```typescript
// Automated courier booking
Payment Verified → Select Courier (TCS/Leopards) → Book Shipment → Get Tracking Number
```

### 5. Tracking Agent
```typescript
// Real-time status updates
Poll Courier APIs → Update Order Status → Send Customer Notifications
```

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js 14 App Router
│   ├── (dashboard)/       # Admin dashboard
│   ├── api/              # API routes
│   ├── order/            # Customer order flow
│   ├── tracking/         # Order tracking
│   └── page.tsx          # Landing page
├── agents/               # AI agent implementations
│   ├── base-agent.ts     # Base agent class
│   ├── order-intake-agent.ts
│   ├── invoice-agent.ts
│   ├── payment-verification-agent.ts
│   └── delivery-agent.ts
├── components/           # Reusable UI components
├── lib/                  # Utilities and configurations
│   ├── db.ts            # Database connection
│   ├── config.ts        # App configuration
│   └── utils.ts         # Helper functions
├── services/            # External API integrations
└── types/               # TypeScript definitions
```

## 🎨 Design System

### Color Palette
- **Primary**: Mango Yellow (#FFC107)
- **Secondary**: Fresh Leaf Green (#4CAF50)
- **Background**: Tropical Cream (#FFF8E1)
- **Text**: Charcoal Black (#212121)
- **Success**: Emerald Green (#43A047)
- **Warning**: Amber (#FFB300)
- **Error**: Tomato Red (#E53935)

### Typography
- **Headings**: Poppins (600-800)
- **Body**: Inter
- **Monospace**: JetBrains Mono

## 🔌 API Endpoints

### Orders
- `POST /api/orders` - Create new order
- `GET /api/orders` - List orders (admin)
- `GET /api/orders/[id]` - Get order details
- `PATCH /api/orders/[id]` - Update order status

### Admin
- `GET /api/admin/stats` - Dashboard statistics
- `POST /api/admin/verify-payment` - Manual payment verification

### Agents
- `POST /api/agents/process-order` - Trigger order processing
- `POST /api/agents/verify-payment` - Payment verification
- `POST /api/agents/book-delivery` - Delivery booking

## 🧪 Testing

### Demo Order Numbers
Try these sample order numbers on the tracking page:
- `MNG-DEMO-001` - Delivered order
- `MNG-DEMO-002` - In transit
- `MNG-DEMO-003` - Payment pending
- `MNG-DEMO-004` - Processing

### Test Payment Verification
Upload sample payment screenshots to test OCR functionality.

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy automatically

### Railway
1. Connect GitHub repository
2. Add PostgreSQL service
3. Set environment variables
4. Deploy

### Manual Deployment
1. Build the application: `npm run build`
2. Start production server: `npm start`
3. Ensure database is accessible
4. Configure reverse proxy (nginx)

## 🔧 Configuration

### Business Settings
Adjust pricing and delivery charges in `.env`:
```env
MANGO_PRICE_PER_KG=150
DELIVERY_CHARGE_KARACHI=200
DELIVERY_CHARGE_LAHORE=250
```

### AI Configuration
Configure OpenAI settings in `src/lib/config.ts`:
```typescript
openai: {
  apiKey: process.env.OPENAI_API_KEY!,
  model: 'gpt-4',
  temperature: 0.7,
}
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Email**: <EMAIL>
- **Phone**: +92-300-1234567
- **Documentation**: [docs.mangoease.com](https://docs.mangoease.com)

## 🙏 Acknowledgments

- **Anthropic Claude** for AI assistance
- **Next.js Team** for the amazing framework
- **Tailwind CSS** for the utility-first CSS framework
- **Prisma** for the excellent ORM
- **Framer Motion** for smooth animations

---

**Built with ❤️ for the mango lovers of Pakistan** 🥭🇵🇰
