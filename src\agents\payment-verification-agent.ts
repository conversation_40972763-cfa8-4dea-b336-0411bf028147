import { BaseAgent } from './base-agent'
import { AgentT<PERSON>, AgentContext, AgentResult, OCRResult } from '@/types'
import { prisma } from '@/lib/db'
import { PaymentMethod, VerificationStatus, PaymentStatus } from '@prisma/client'
import Tesseract from 'tesseract.js'

interface PaymentVerificationInput {
  orderId: string
  paymentMethod: PaymentMethod
  screenshotFile?: File
  screenshotUrl?: string
  transactionId?: string
  amount: number
}

export class PaymentVerificationAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super(AgentType.PAYMENT_VERIFIER, context)
  }

  async execute(input: PaymentVerificationInput): Promise<AgentResult> {
    try {
      await this.logAction('payment_verification_started', input, null, true)

      // Validate input
      const requiredFields = ['orderId', 'paymentMethod', 'amount']
      if (!this.validateInput(input, requiredFields)) {
        const error = 'Missing required fields for payment verification'
        await this.logAction('validation_failed', input, null, false, error)
        return this.handleError(new Error(error), 'validation')
      }

      // Get order details
      const order = await this.getOrder()
      if (!order) {
        const error = 'Order not found'
        await this.logAction('order_not_found', input, null, false, error)
        return this.handleError(new Error(error), 'order_lookup')
      }

      // Check if payment already exists
      if (order.payment) {
        await this.logAction('payment_already_exists', input, { paymentId: order.payment.id }, true)
        return {
          success: true,
          data: {
            payment: order.payment,
            message: 'Payment record already exists',
          },
        }
      }

      // Create payment record
      const payment = await prisma.payment.create({
        data: {
          orderId: input.orderId,
          method: input.paymentMethod,
          amount: input.amount,
          transactionId: input.transactionId,
          screenshotUrl: input.screenshotUrl,
          verificationStatus: VerificationStatus.PENDING,
        },
      })

      let verificationResult: OCRResult | null = null

      // Process screenshot if provided
      if (input.screenshotFile || input.screenshotUrl) {
        try {
          verificationResult = await this.processPaymentScreenshot(
            input.screenshotFile || input.screenshotUrl!,
            input.amount,
            input.transactionId
          )

          await this.logAction('ocr_processing_completed', input, verificationResult, true)
        } catch (ocrError) {
          await this.logAction('ocr_processing_failed', input, null, false, ocrError instanceof Error ? ocrError.message : 'OCR failed')
          // Continue without OCR - manual verification can be done
        }
      }

      // Determine verification status
      let verificationStatus = VerificationStatus.PENDING
      let paymentStatus = PaymentStatus.PENDING

      if (verificationResult) {
        const isAmountMatch = this.verifyAmount(verificationResult.amount, input.amount)
        const isTransactionMatch = this.verifyTransactionId(verificationResult.transactionId, input.transactionId)

        if (isAmountMatch && isTransactionMatch && verificationResult.confidence > 0.8) {
          verificationStatus = VerificationStatus.VERIFIED
          paymentStatus = PaymentStatus.PAID
        } else if (verificationResult.confidence > 0.6) {
          verificationStatus = VerificationStatus.MANUAL_REVIEW
        } else {
          verificationStatus = VerificationStatus.REJECTED
        }
      }

      // Update payment record
      const updatedPayment = await prisma.payment.update({
        where: { id: payment.id },
        data: {
          verificationStatus,
          verifiedAt: verificationStatus === VerificationStatus.VERIFIED ? new Date() : null,
        },
      })

      // Update order payment status
      await prisma.order.update({
        where: { id: input.orderId },
        data: { paymentStatus },
      })

      const result = {
        payment: updatedPayment,
        verificationResult,
        autoVerified: verificationStatus === VerificationStatus.VERIFIED,
      }

      await this.logAction('payment_verification_completed', input, result, true)

      return {
        success: true,
        data: result,
        nextAction: verificationStatus === VerificationStatus.VERIFIED ? 'book_delivery' : 'await_manual_review',
      }
    } catch (error) {
      await this.logAction('payment_verification_failed', input, null, false, error instanceof Error ? error.message : 'Unknown error')
      return this.handleError(error, 'payment_verification')
    }
  }

  private async processPaymentScreenshot(
    source: File | string,
    expectedAmount: number,
    expectedTransactionId?: string
  ): Promise<OCRResult> {
    try {
      let imageSource: string

      if (typeof source === 'string') {
        imageSource = source
      } else {
        // Convert File to data URL
        const arrayBuffer = await source.arrayBuffer()
        const base64 = Buffer.from(arrayBuffer).toString('base64')
        imageSource = `data:${source.type};base64,${base64}`
      }

      // Perform OCR using Tesseract.js
      const { data } = await Tesseract.recognize(imageSource, 'eng', {
        logger: m => console.log(m)
      })

      const extractedText = data.text
      const confidence = data.confidence / 100

      // Extract payment information from text
      const extractedAmount = this.extractAmount(extractedText)
      const extractedTransactionId = this.extractTransactionId(extractedText)
      const extractedTimestamp = this.extractTimestamp(extractedText)

      return {
        text: extractedText,
        confidence,
        amount: extractedAmount,
        transactionId: extractedTransactionId,
        timestamp: extractedTimestamp,
      }
    } catch (error) {
      throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private extractAmount(text: string): number | undefined {
    // Look for patterns like "Rs. 1,500", "PKR 1500", "1,500.00"
    const amountPatterns = [
      /(?:rs\.?|pkr)\s*([0-9,]+(?:\.[0-9]{2})?)/i,
      /([0-9,]+(?:\.[0-9]{2})?)\s*(?:rs\.?|pkr)/i,
      /amount[:\s]*([0-9,]+(?:\.[0-9]{2})?)/i,
      /total[:\s]*([0-9,]+(?:\.[0-9]{2})?)/i,
    ]

    for (const pattern of amountPatterns) {
      const match = text.match(pattern)
      if (match) {
        const amountStr = match[1].replace(/,/g, '')
        const amount = parseFloat(amountStr)
        if (!isNaN(amount) && amount > 0) {
          return amount
        }
      }
    }

    return undefined
  }

  private extractTransactionId(text: string): string | undefined {
    // Look for patterns like transaction IDs, reference numbers
    const transactionPatterns = [
      /(?:transaction|trans|ref|reference)[:\s#]*([a-z0-9]{6,20})/i,
      /(?:id|number)[:\s#]*([a-z0-9]{6,20})/i,
      /([a-z0-9]{10,20})/i, // Generic alphanumeric pattern
    ]

    for (const pattern of transactionPatterns) {
      const match = text.match(pattern)
      if (match) {
        return match[1].toUpperCase()
      }
    }

    return undefined
  }

  private extractTimestamp(text: string): Date | undefined {
    // Look for date/time patterns
    const datePatterns = [
      /(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/,
      /(\d{2,4}[-\/]\d{1,2}[-\/]\d{1,2})/,
    ]

    for (const pattern of datePatterns) {
      const match = text.match(pattern)
      if (match) {
        const date = new Date(match[1])
        if (!isNaN(date.getTime())) {
          return date
        }
      }
    }

    return undefined
  }

  private verifyAmount(extractedAmount: number | undefined, expectedAmount: number): boolean {
    if (!extractedAmount) return false
    
    // Allow 1% tolerance for amount verification
    const tolerance = expectedAmount * 0.01
    return Math.abs(extractedAmount - expectedAmount) <= tolerance
  }

  private verifyTransactionId(extractedId: string | undefined, expectedId: string | undefined): boolean {
    if (!extractedId || !expectedId) return true // Skip verification if not provided
    
    return extractedId.toLowerCase() === expectedId.toLowerCase()
  }

  async manualVerification(paymentId: string, approved: boolean, notes?: string): Promise<AgentResult> {
    try {
      const payment = await prisma.payment.findUnique({
        where: { id: paymentId },
        include: { order: true },
      })

      if (!payment) {
        return {
          success: false,
          error: 'Payment not found',
        }
      }

      const verificationStatus = approved ? VerificationStatus.VERIFIED : VerificationStatus.REJECTED
      const paymentStatus = approved ? PaymentStatus.PAID : PaymentStatus.FAILED

      // Update payment
      const updatedPayment = await prisma.payment.update({
        where: { id: paymentId },
        data: {
          verificationStatus,
          verifiedAt: approved ? new Date() : null,
        },
      })

      // Update order
      await prisma.order.update({
        where: { id: payment.orderId },
        data: { paymentStatus },
      })

      await this.logAction('manual_verification_completed', { paymentId, approved, notes }, updatedPayment, true)

      return {
        success: true,
        data: {
          payment: updatedPayment,
          approved,
        },
        nextAction: approved ? 'book_delivery' : 'payment_rejected',
      }
    } catch (error) {
      return this.handleError(error, 'manual_verification')
    }
  }
}
