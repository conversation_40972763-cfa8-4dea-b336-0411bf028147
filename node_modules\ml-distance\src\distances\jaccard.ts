import { NumberArray } from 'cheminfo-types';

import kuma<PERSON><PERSON><PERSON><PERSON><PERSON> from '../similarities/kumar<PERSON><PERSON><PERSON><PERSON>';
/**
 *Returns Jaccard distance between vectors a and b
 * @link [J<PERSON><PERSON> algorithm](https://www.naun.org/main/NAUN/ijmmas/mmmas-49.pdf)
 * @param a - first vector
 * @param b - second vector
 *
 */
export default function jaccard(a: NumberArray, b: NumberArray): number {
  return 1 - kuma<PERSON><PERSON><PERSON><PERSON><PERSON>(a, b);
}
