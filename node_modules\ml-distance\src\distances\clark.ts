import { NumberArray } from 'cheminfo-types';
/**
 *Returns the <PERSON> distance between vectors a and b
 * @link [<PERSON> algorithm](https://www.naun.org/main/NAUN/ijmmas/mmmas-49.pdf)
 * @param a - first vector
 * @param b - second vector
 *
 */
export default function clark(a: NumberArray, b: NumberArray): number {
  let d = 0;
  for (let i = 0; i < a.length; i++) {
    d += (Math.abs(a[i] - b[i]) / (a[i] + b[i])) ** 2;
  }
  return Math.sqrt(d);
}
