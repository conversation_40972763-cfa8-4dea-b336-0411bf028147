import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { OrderStatus, PaymentStatus } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    // Get date ranges
    const now = new Date()
    const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    // Get all orders count and revenue
    const [
      totalOrders,
      totalRevenue,
      pendingOrders,
      paidOrders,
      shippedOrders,
      deliveredOrders,
      todayOrders,
      todayRevenue
    ] = await Promise.all([
      // Total orders
      prisma.order.count(),
      
      // Total revenue (from paid orders)
      prisma.order.aggregate({
        where: { paymentStatus: PaymentStatus.PAID },
        _sum: { totalAmount: true }
      }),
      
      // Pending orders
      prisma.order.count({
        where: { status: OrderStatus.PENDING }
      }),
      
      // Paid orders
      prisma.order.count({
        where: { paymentStatus: PaymentStatus.PAID }
      }),
      
      // Shipped orders
      prisma.order.count({
        where: { status: OrderStatus.SHIPPED }
      }),
      
      // Delivered orders
      prisma.order.count({
        where: { status: OrderStatus.DELIVERED }
      }),
      
      // Today's orders
      prisma.order.count({
        where: {
          createdAt: {
            gte: startOfToday
          }
        }
      }),
      
      // Today's revenue
      prisma.order.aggregate({
        where: {
          paymentStatus: PaymentStatus.PAID,
          createdAt: {
            gte: startOfToday
          }
        },
        _sum: { totalAmount: true }
      })
    ])

    // Calculate average order value
    const averageOrderValue = totalOrders > 0 
      ? (totalRevenue._sum.totalAmount || 0) / totalOrders 
      : 0

    // Get recent orders for activity feed
    const recentOrders = await prisma.order.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        payment: true,
        delivery: true
      }
    })

    // Get order status distribution
    const statusDistribution = await prisma.order.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })

    // Get payment status distribution
    const paymentDistribution = await prisma.order.groupBy({
      by: ['paymentStatus'],
      _count: {
        paymentStatus: true
      }
    })

    // Get daily revenue for the last 30 days
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    
    const dailyRevenue = await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as orders,
        COALESCE(SUM(total_amount), 0) as revenue
      FROM orders 
      WHERE created_at >= ${thirtyDaysAgo}
        AND payment_status = 'PAID'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `

    // Get top cities by order count
    const topCities = await prisma.order.groupBy({
      by: ['city'],
      _count: {
        city: true
      },
      _sum: {
        totalAmount: true
      },
      orderBy: {
        _count: {
          city: 'desc'
        }
      },
      take: 10
    })

    const stats = {
      totalOrders,
      pendingOrders,
      paidOrders,
      shippedOrders,
      deliveredOrders,
      totalRevenue: totalRevenue._sum.totalAmount || 0,
      todayRevenue: todayRevenue._sum.totalAmount || 0,
      averageOrderValue,
      recentOrders,
      statusDistribution,
      paymentDistribution,
      dailyRevenue,
      topCities
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Admin stats error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch admin statistics'
    }, { status: 500 })
  }
}
