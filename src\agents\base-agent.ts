import { prisma } from '@/lib/db'
import { Agent<PERSON><PERSON>, AgentContext, AgentResult } from '@/types'

export abstract class BaseAgent {
  protected agentType: AgentType
  protected context: AgentContext

  constructor(agentType: AgentType, context: AgentContext) {
    this.agentType = agentType
    this.context = context
  }

  abstract execute(input: any): Promise<AgentResult>

  protected async logAction(
    action: string,
    input: any,
    output: any,
    success: boolean,
    error?: string
  ): Promise<void> {
    try {
      await prisma.agentLog.create({
        data: {
          orderId: this.context.orderId,
          agentType: this.agentType,
          action,
          input: input ? JSON.parse(JSON.stringify(input)) : null,
          output: output ? JSON.parse(JSON.stringify(output)) : null,
          success,
          error,
        },
      })
    } catch (logError) {
      console.error('Failed to log agent action:', logError)
    }
  }

  protected async updateOrderStatus(status: any): Promise<void> {
    try {
      await prisma.order.update({
        where: { id: this.context.orderId },
        data: { status },
      })
    } catch (error) {
      console.error('Failed to update order status:', error)
    }
  }

  protected async getOrder() {
    return await prisma.order.findUnique({
      where: { id: this.context.orderId },
      include: {
        invoice: true,
        payment: true,
        delivery: true,
        agentLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    })
  }

  protected handleError(error: any, action: string): AgentResult {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    console.error(`${this.agentType} - ${action} failed:`, error)
    
    return {
      success: false,
      error: errorMessage,
    }
  }

  protected validateInput(input: any, requiredFields: string[]): boolean {
    if (!input || typeof input !== 'object') {
      return false
    }

    return requiredFields.every(field => {
      const value = input[field]
      return value !== undefined && value !== null && value !== ''
    })
  }

  protected async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  protected sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      return input.trim().replace(/[<>]/g, '')
    }
    
    if (typeof input === 'object' && input !== null) {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(input)) {
        sanitized[key] = this.sanitizeInput(value)
      }
      return sanitized
    }
    
    return input
  }

  protected formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  protected formatPhoneNumber(phone: string): string {
    const cleaned = phone.replace(/\D/g, '')
    if (cleaned.startsWith('0')) {
      return `+92${cleaned.substring(1)}`
    } else if (!cleaned.startsWith('92')) {
      return `+92${cleaned}`
    }
    return `+${cleaned}`
  }
}
