'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Package, 
  CheckCircle, 
  Clock, 
  Truck, 
  MapPin, 
  Phone, 
  Mail,
  ArrowLeft,
  Download,
  Upload,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'
import { config } from '@/lib/config'
import { formatCurrency, formatDate, getStatusColor } from '@/lib/utils'
import { OrderWithDetails } from '@/types'

interface TrackingPageProps {
  params: { id: string }
}

export default function TrackingDetailPage({ params }: TrackingPageProps) {
  const [order, setOrder] = useState<OrderWithDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchOrderDetails()
  }, [params.id])

  const fetchOrderDetails = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/orders/${params.id}`)
      const result = await response.json()

      if (result.success) {
        setOrder(result.data)
      } else {
        setError(result.error || 'Order not found')
      }
    } catch (err) {
      setError('Failed to fetch order details')
    } finally {
      setLoading(false)
    }
  }

  const getOrderStatusSteps = () => {
    if (!order) return []

    const steps = [
      {
        id: 'PENDING',
        title: 'Order Placed',
        description: 'Your order has been received',
        icon: Package,
        completed: true,
        timestamp: order.createdAt
      },
      {
        id: 'CONFIRMED',
        title: 'Order Confirmed',
        description: 'Order details verified',
        icon: CheckCircle,
        completed: ['CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED'].includes(order.status),
        timestamp: order.status !== 'PENDING' ? order.updatedAt : null
      },
      {
        id: 'PROCESSING',
        title: 'Processing',
        description: 'Mangoes being prepared for shipment',
        icon: Clock,
        completed: ['PROCESSING', 'SHIPPED', 'DELIVERED'].includes(order.status),
        timestamp: order.status === 'PROCESSING' ? order.updatedAt : null
      },
      {
        id: 'SHIPPED',
        title: 'Shipped',
        description: 'Package is on the way',
        icon: Truck,
        completed: ['SHIPPED', 'DELIVERED'].includes(order.status),
        timestamp: order.delivery?.bookedAt || (order.status === 'SHIPPED' ? order.updatedAt : null)
      },
      {
        id: 'DELIVERED',
        title: 'Delivered',
        description: 'Package delivered successfully',
        icon: CheckCircle,
        completed: order.status === 'DELIVERED',
        timestamp: order.delivery?.deliveredAt || (order.status === 'DELIVERED' ? order.updatedAt : null)
      }
    ]

    return steps
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-cream to-white flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading order details...</p>
        </div>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-cream to-white">
        <header className="bg-white shadow-md">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Link href="/" className="text-2xl font-bold text-gradient font-heading">
              🥭 {config.app.name}
            </Link>
          </div>
        </header>
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <AlertCircle className="w-10 h-10 text-red-500" />
          </div>
          
          <h1 className="text-3xl font-bold text-charcoal mb-4 font-heading">
            Order Not Found
          </h1>
          
          <p className="text-gray-600 mb-8">
            {error || 'The order you are looking for does not exist or has been removed.'}
          </p>
          
          <div className="space-y-4">
            <Link href="/tracking" className="btn-primary">
              Try Another Order Number
            </Link>
            <Link href="/" className="btn-outline">
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const statusSteps = getOrderStatusSteps()

  return (
    <div className="min-h-screen bg-gradient-to-b from-cream to-white">
      {/* Header */}
      <header className="bg-white shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-gradient font-heading">
              🥭 {config.app.name}
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/tracking" className="text-gray-600 hover:text-leaf-600 transition-colors flex items-center">
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back to Tracking
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Order Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-8"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-charcoal font-heading">
                Order #{order.orderNumber}
              </h1>
              <p className="text-gray-600 mt-1">
                Placed on {formatDate(new Date(order.createdAt))}
              </p>
            </div>
            
            <div className="mt-4 sm:mt-0">
              <span className={`status-badge ${getStatusColor(order.status)}`}>
                {order.status.replace('_', ' ')}
              </span>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Order Status Timeline */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              className="card"
            >
              <h2 className="text-xl font-semibold text-charcoal mb-6 font-heading">
                Order Status
              </h2>
              
              <div className="space-y-6">
                {statusSteps.map((step, index) => (
                  <div key={step.id} className="flex items-start">
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                      step.completed 
                        ? 'bg-leaf-500 text-white' 
                        : 'bg-gray-200 text-gray-400'
                    }`}>
                      <step.icon className="w-5 h-5" />
                    </div>
                    
                    <div className="ml-4 flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className={`font-medium ${
                          step.completed ? 'text-charcoal' : 'text-gray-400'
                        }`}>
                          {step.title}
                        </h3>
                        {step.timestamp && (
                          <span className="text-sm text-gray-500">
                            {formatDate(new Date(step.timestamp))}
                          </span>
                        )}
                      </div>
                      <p className={`text-sm mt-1 ${
                        step.completed ? 'text-gray-600' : 'text-gray-400'
                      }`}>
                        {step.description}
                      </p>
                    </div>
                    
                    {index < statusSteps.length - 1 && (
                      <div className={`absolute left-5 mt-10 w-0.5 h-6 ${
                        step.completed ? 'bg-leaf-500' : 'bg-gray-200'
                      }`} style={{ marginLeft: '-1px' }} />
                    )}
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Delivery Information */}
            {order.delivery && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="card"
              >
                <h2 className="text-xl font-semibold text-charcoal mb-6 font-heading">
                  Delivery Information
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-gray-800 mb-2">Courier Service</h3>
                    <p className="text-gray-600">{order.delivery.courierService}</p>
                  </div>
                  
                  {order.delivery.trackingNumber && (
                    <div>
                      <h3 className="font-medium text-gray-800 mb-2">Tracking Number</h3>
                      <p className="text-gray-600 font-mono">{order.delivery.trackingNumber}</p>
                    </div>
                  )}
                  
                  <div className="md:col-span-2">
                    <h3 className="font-medium text-gray-800 mb-2">Delivery Address</h3>
                    <p className="text-gray-600">
                      {order.customerAddress}<br />
                      {order.city}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Payment Information */}
            {order.payment && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="card"
              >
                <h2 className="text-xl font-semibold text-charcoal mb-6 font-heading">
                  Payment Information
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-gray-800 mb-2">Payment Method</h3>
                    <p className="text-gray-600">{order.payment.method.replace('_', ' ')}</p>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-gray-800 mb-2">Payment Status</h3>
                    <span className={`status-badge ${getStatusColor(order.payment.verificationStatus)}`}>
                      {order.payment.verificationStatus.replace('_', ' ')}
                    </span>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-gray-800 mb-2">Amount</h3>
                    <p className="text-gray-600 font-semibold">{formatCurrency(order.payment.amount)}</p>
                  </div>
                  
                  {order.payment.transactionId && (
                    <div>
                      <h3 className="font-medium text-gray-800 mb-2">Transaction ID</h3>
                      <p className="text-gray-600 font-mono">{order.payment.transactionId}</p>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="card"
            >
              <h3 className="text-lg font-semibold text-charcoal mb-4 font-heading">
                Order Summary
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Fresh Mangoes ({order.quantity} kg)</span>
                  <span>{formatCurrency(order.quantity * order.pricePerKg)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery Charges</span>
                  <span>{formatCurrency(order.deliveryCharge)}</span>
                </div>
                <div className="border-t pt-3 flex justify-between font-semibold">
                  <span>Total Amount</span>
                  <span className="text-leaf-600">{formatCurrency(order.totalAmount)}</span>
                </div>
              </div>
            </motion.div>

            {/* Customer Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="card"
            >
              <h3 className="text-lg font-semibold text-charcoal mb-4 font-heading">
                Customer Information
              </h3>
              
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Phone className="w-4 h-4 text-gray-400 mr-2" />
                  <span>{order.customerPhone}</span>
                </div>
                {order.customerEmail && (
                  <div className="flex items-center">
                    <Mail className="w-4 h-4 text-gray-400 mr-2" />
                    <span>{order.customerEmail}</span>
                  </div>
                )}
                <div className="flex items-start">
                  <MapPin className="w-4 h-4 text-gray-400 mr-2 mt-0.5" />
                  <span>{order.customerAddress}, {order.city}</span>
                </div>
              </div>
            </motion.div>

            {/* Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="card"
            >
              <h3 className="text-lg font-semibold text-charcoal mb-4 font-heading">
                Actions
              </h3>
              
              <div className="space-y-3">
                {order.invoice?.pdfUrl && (
                  <a
                    href={order.invoice.pdfUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-outline w-full text-center"
                  >
                    <Download className="inline w-4 h-4 mr-2" />
                    Download Invoice
                  </a>
                )}
                
                {order.paymentStatus === 'PENDING' && (
                  <Link
                    href={`/payment/${order.id}`}
                    className="btn-primary w-full text-center"
                  >
                    <Upload className="inline w-4 h-4 mr-2" />
                    Upload Payment
                  </Link>
                )}
                
                <Link href="/order" className="btn-outline w-full text-center">
                  Place New Order
                </Link>
              </div>
            </motion.div>

            {/* Support */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="card"
            >
              <h3 className="text-lg font-semibold text-charcoal mb-4 font-heading">
                Need Help?
              </h3>
              
              <div className="space-y-3 text-sm">
                <a href={`tel:${config.app.supportPhone}`} className="flex items-center text-leaf-600 hover:text-leaf-700">
                  <Phone className="w-4 h-4 mr-2" />
                  {config.app.supportPhone}
                </a>
                <a href={`mailto:${config.app.supportEmail}`} className="flex items-center text-leaf-600 hover:text-leaf-700">
                  <Mail className="w-4 h-4 mr-2" />
                  {config.app.supportEmail}
                </a>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
