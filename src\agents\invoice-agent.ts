import { BaseAgent } from './base-agent'
import { Agent<PERSON><PERSON>, AgentContext, AgentResult } from '@/types'
import { prisma } from '@/lib/db'
import { generateInvoiceNumber } from '@/lib/utils'
import { config } from '@/lib/config'
import jsPDF from 'jspdf'

interface InvoiceGenerationInput {
  orderId: string
  generatePDF?: boolean
}

export class InvoiceAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super(AgentType.INVOICE_GENERATOR, context)
  }

  async execute(input: InvoiceGenerationInput): Promise<AgentResult> {
    try {
      await this.logAction('invoice_generation_started', input, null, true)

      // Get order details
      const order = await this.getOrder()
      if (!order) {
        const error = 'Order not found'
        await this.logAction('order_not_found', input, null, false, error)
        return this.handleError(new Error(error), 'order_lookup')
      }

      // Check if invoice already exists
      if (order.invoice) {
        await this.logAction('invoice_already_exists', input, { invoiceId: order.invoice.id }, true)
        return {
          success: true,
          data: {
            invoice: order.invoice,
            message: 'Invoice already exists',
          },
        }
      }

      // Generate invoice number
      const invoiceNumber = generateInvoiceNumber()

      // Create invoice record
      const invoice = await prisma.invoice.create({
        data: {
          orderId: order.id,
          invoiceNumber,
        },
      })

      let pdfUrl = null
      if (input.generatePDF) {
        try {
          pdfUrl = await this.generateInvoicePDF(order, invoice)
          
          // Update invoice with PDF URL
          await prisma.invoice.update({
            where: { id: invoice.id },
            data: { pdfUrl },
          })
        } catch (pdfError) {
          console.error('PDF generation failed:', pdfError)
          // Continue without PDF - can be generated later
        }
      }

      const result = {
        invoice: {
          ...invoice,
          pdfUrl,
        },
        order,
      }

      await this.logAction('invoice_generated', input, result, true)

      return {
        success: true,
        data: result,
        nextAction: 'await_payment',
      }
    } catch (error) {
      await this.logAction('invoice_generation_failed', input, null, false, error instanceof Error ? error.message : 'Unknown error')
      return this.handleError(error, 'invoice_generation')
    }
  }

  private async generateInvoicePDF(order: any, invoice: any): Promise<string> {
    try {
      const doc = new jsPDF()
      
      // Set font
      doc.setFont('helvetica')
      
      // Header
      doc.setFontSize(24)
      doc.setTextColor(255, 193, 7) // Mango color
      doc.text('🥭 MangoEase AI', 20, 30)
      
      doc.setFontSize(12)
      doc.setTextColor(0, 0, 0)
      doc.text(config.app.tagline, 20, 40)
      
      // Invoice title
      doc.setFontSize(20)
      doc.text('INVOICE', 150, 30)
      
      // Invoice details
      doc.setFontSize(10)
      doc.text(`Invoice #: ${invoice.invoiceNumber}`, 150, 45)
      doc.text(`Order #: ${order.orderNumber}`, 150, 55)
      doc.text(`Date: ${new Date().toLocaleDateString('en-PK')}`, 150, 65)
      
      // Customer details
      doc.setFontSize(14)
      doc.text('Bill To:', 20, 80)
      
      doc.setFontSize(10)
      doc.text(order.customerName, 20, 95)
      doc.text(order.customerPhone, 20, 105)
      if (order.customerEmail) {
        doc.text(order.customerEmail, 20, 115)
      }
      
      // Address
      const addressLines = this.splitText(order.customerAddress, 40)
      let yPos = order.customerEmail ? 125 : 115
      addressLines.forEach((line: string) => {
        doc.text(line, 20, yPos)
        yPos += 10
      })
      doc.text(order.city, 20, yPos)
      
      // Items table
      const tableStartY = yPos + 30
      
      // Table header
      doc.setFillColor(76, 175, 80) // Leaf green
      doc.rect(20, tableStartY, 170, 10, 'F')
      doc.setTextColor(255, 255, 255)
      doc.setFontSize(10)
      doc.text('Description', 25, tableStartY + 7)
      doc.text('Quantity', 100, tableStartY + 7)
      doc.text('Rate', 130, tableStartY + 7)
      doc.text('Amount', 160, tableStartY + 7)
      
      // Table content
      doc.setTextColor(0, 0, 0)
      const itemY = tableStartY + 20
      doc.text('Fresh Mangoes (Premium Quality)', 25, itemY)
      doc.text(`${order.quantity} kg`, 100, itemY)
      doc.text(this.formatCurrency(order.pricePerKg), 130, itemY)
      doc.text(this.formatCurrency(order.quantity * order.pricePerKg), 160, itemY)
      
      // Delivery charge
      const deliveryY = itemY + 15
      doc.text('Delivery Charges', 25, deliveryY)
      doc.text('1', 100, deliveryY)
      doc.text(this.formatCurrency(order.deliveryCharge), 130, deliveryY)
      doc.text(this.formatCurrency(order.deliveryCharge), 160, deliveryY)
      
      // Total
      const totalY = deliveryY + 20
      doc.setFontSize(12)
      doc.setFont('helvetica', 'bold')
      doc.text('Total Amount:', 130, totalY)
      doc.text(this.formatCurrency(order.totalAmount), 160, totalY)
      
      // Payment instructions
      doc.setFont('helvetica', 'normal')
      doc.setFontSize(10)
      const instructionsY = totalY + 30
      doc.text('Payment Instructions:', 20, instructionsY)
      doc.text('• Pay via JazzCash, EasyPaisa, or Bank Transfer', 20, instructionsY + 15)
      doc.text('• Upload payment screenshot on our website', 20, instructionsY + 25)
      doc.text('• Your order will be processed after payment verification', 20, instructionsY + 35)
      
      // Footer
      doc.setFontSize(8)
      doc.setTextColor(128, 128, 128)
      doc.text('Thank you for choosing MangoEase AI!', 20, 280)
      doc.text(`Support: ${config.app.supportPhone} | ${config.app.supportEmail}`, 20, 290)
      
      // Convert to base64 and return URL (in real app, upload to storage)
      const pdfBase64 = doc.output('datauristring')
      
      // In a real implementation, you would upload this to Supabase Storage
      // For now, we'll return a placeholder URL
      return `/invoices/${invoice.invoiceNumber}.pdf`
      
    } catch (error) {
      console.error('PDF generation error:', error)
      throw new Error('Failed to generate PDF')
    }
  }

  private splitText(text: string, maxLength: number): string[] {
    const words = text.split(' ')
    const lines: string[] = []
    let currentLine = ''
    
    words.forEach(word => {
      if ((currentLine + word).length <= maxLength) {
        currentLine += (currentLine ? ' ' : '') + word
      } else {
        if (currentLine) lines.push(currentLine)
        currentLine = word
      }
    })
    
    if (currentLine) lines.push(currentLine)
    return lines
  }

  async getInvoiceData(orderId: string): Promise<AgentResult> {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          invoice: true,
        },
      })

      if (!order) {
        return {
          success: false,
          error: 'Order not found',
        }
      }

      if (!order.invoice) {
        return {
          success: false,
          error: 'Invoice not found for this order',
        }
      }

      return {
        success: true,
        data: {
          order,
          invoice: order.invoice,
        },
      }
    } catch (error) {
      return this.handleError(error, 'get_invoice_data')
    }
  }

  async regenerateInvoicePDF(invoiceId: string): Promise<AgentResult> {
    try {
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
        include: {
          order: true,
        },
      })

      if (!invoice) {
        return {
          success: false,
          error: 'Invoice not found',
        }
      }

      const pdfUrl = await this.generateInvoicePDF(invoice.order, invoice)
      
      await prisma.invoice.update({
        where: { id: invoiceId },
        data: { pdfUrl },
      })

      return {
        success: true,
        data: {
          invoice: {
            ...invoice,
            pdfUrl,
          },
        },
      }
    } catch (error) {
      return this.handleError(error, 'regenerate_pdf')
    }
  }
}
