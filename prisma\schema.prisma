// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  role      Role     @default(CUSTOMER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  orders Order[]

  @@map("users")
}

model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  customerName    String
  customerPhone   String
  customerEmail   String?
  customerAddress String
  city            String
  
  // Order Details
  quantity        Float       // in kg
  pricePerKg      Float
  deliveryCharge  Float
  totalAmount     Float
  
  // Status
  status          OrderStatus @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING)
  
  // Timestamps
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  
  // Relations
  userId          String?
  user            User?       @relation(fields: [userId], references: [id])
  invoice         Invoice?
  payment         Payment?
  delivery        Delivery?
  agentLogs       AgentLog[]

  @@map("orders")
}

model Invoice {
  id          String   @id @default(cuid())
  orderId     String   @unique
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  invoiceNumber String @unique
  pdfUrl        String?
  generatedAt   DateTime @default(now())
  
  @@map("invoices")
}

model Payment {
  id              String        @id @default(cuid())
  orderId         String        @unique
  order           Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  method          PaymentMethod
  amount          Float
  transactionId   String?
  screenshotUrl   String?
  verificationStatus VerificationStatus @default(PENDING)
  verifiedAt      DateTime?
  
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  @@map("payments")
}

model Delivery {
  id              String         @id @default(cuid())
  orderId         String         @unique
  order           Order          @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  courierService  CourierService
  trackingNumber  String?
  pickupAddress   String
  deliveryAddress String
  
  status          DeliveryStatus @default(PENDING)
  bookedAt        DateTime?
  pickedUpAt      DateTime?
  deliveredAt     DateTime?
  
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  
  @@map("deliveries")
}

model AgentLog {
  id        String    @id @default(cuid())
  orderId   String
  order     Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  agentType AgentType
  action    String
  input     Json?
  output    Json?
  success   Boolean
  error     String?
  
  createdAt DateTime  @default(now())
  
  @@map("agent_logs")
}

// Enums
enum Role {
  ADMIN
  CUSTOMER
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PaymentMethod {
  JAZZCASH
  EASYPAISA
  BANK_TRANSFER
  CASH_ON_DELIVERY
}

enum VerificationStatus {
  PENDING
  VERIFIED
  REJECTED
  MANUAL_REVIEW
}

enum CourierService {
  TCS
  LEOPARDS
  MP_COURIER
  MANUAL
}

enum DeliveryStatus {
  PENDING
  BOOKED
  PICKED_UP
  IN_TRANSIT
  OUT_FOR_DELIVERY
  DELIVERED
  FAILED
}

enum AgentType {
  ORDER_INTAKE
  INVOICE_GENERATOR
  PAYMENT_VERIFIER
  DELIVERY_BOOKER
  TRACKING_UPDATER
  NOTIFICATION_SENDER
}
