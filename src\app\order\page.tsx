'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ShoppingCart, 
  Calculator, 
  MapPin, 
  Phone, 
  Mail, 
  User,
  ArrowRight,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'
import { config, calculateOrderTotal } from '@/lib/config'
import { formatCurrency } from '@/lib/utils'

interface OrderForm {
  customerName: string
  customerPhone: string
  customerEmail: string
  customerAddress: string
  city: string
  quantity: number
}

export default function OrderPage() {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [orderData, setOrderData] = useState<OrderForm>({
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    customerAddress: '',
    city: '',
    quantity: 1
  })
  const [orderResult, setOrderResult] = useState<any>(null)
  const [errors, setErrors] = useState<string[]>([])

  const cities = [
    'Karachi', 'Lahore', 'Islamabad', 'Rawalpindi', 'Faisalabad',
    'Multan', 'Peshawar', 'Quetta', 'Sialkot', 'Gujranwala', 'Other'
  ]

  const pricing = orderData.city && orderData.quantity > 0 
    ? calculateOrderTotal(orderData.quantity, orderData.city)
    : null

  const handleInputChange = (field: keyof OrderForm, value: string | number) => {
    setOrderData(prev => ({
      ...prev,
      [field]: value
    }))
    setErrors([])
  }

  const validateStep1 = () => {
    const newErrors: string[] = []
    
    if (orderData.quantity <= 0 || orderData.quantity > 100) {
      newErrors.push('Quantity must be between 0.1 and 100 kg')
    }
    
    if (!orderData.city) {
      newErrors.push('Please select your city')
    }

    setErrors(newErrors)
    return newErrors.length === 0
  }

  const validateStep2 = () => {
    const newErrors: string[] = []
    
    if (!orderData.customerName.trim() || orderData.customerName.length < 2) {
      newErrors.push('Name must be at least 2 characters')
    }
    
    const phoneRegex = /^(\+92|92|0)?[0-9]{10}$/
    if (!phoneRegex.test(orderData.customerPhone.replace(/\s|-/g, ''))) {
      newErrors.push('Please provide a valid Pakistani phone number')
    }
    
    if (orderData.customerEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(orderData.customerEmail)) {
      newErrors.push('Please provide a valid email address')
    }
    
    if (!orderData.customerAddress.trim() || orderData.customerAddress.length < 10) {
      newErrors.push('Please provide a complete delivery address')
    }

    setErrors(newErrors)
    return newErrors.length === 0
  }

  const handleNext = () => {
    if (step === 1 && validateStep1()) {
      setStep(2)
    } else if (step === 2 && validateStep2()) {
      setStep(3)
    }
  }

  const handleSubmitOrder = async () => {
    if (!validateStep2()) return

    setLoading(true)
    try {
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      })

      const result = await response.json()

      if (result.success) {
        setOrderResult(result.data)
        setStep(4)
      } else {
        setErrors([result.error || 'Failed to create order'])
      }
    } catch (error) {
      setErrors(['Network error. Please try again.'])
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-cream to-white">
      {/* Header */}
      <header className="bg-white shadow-md">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-gradient font-heading">
              🥭 {config.app.name}
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/tracking" className="text-gray-600 hover:text-leaf-600 transition-colors">
                Track Order
              </Link>
              <Link href="/" className="text-gray-600 hover:text-leaf-600 transition-colors">
                Home
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex items-center justify-center space-x-8">
            {[
              { number: 1, title: 'Order Details', icon: ShoppingCart },
              { number: 2, title: 'Customer Info', icon: User },
              { number: 3, title: 'Review', icon: CheckCircle },
              { number: 4, title: 'Confirmation', icon: CheckCircle }
            ].map((stepItem, index) => (
              <div key={index} className="flex items-center">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                  step > stepItem.number 
                    ? 'bg-leaf-500 border-leaf-500 text-white' 
                    : step === stepItem.number
                    ? 'border-leaf-500 text-leaf-500 bg-white'
                    : 'border-gray-300 text-gray-400 bg-white'
                }`}>
                  <stepItem.icon className="w-5 h-5" />
                </div>
                <div className="ml-3 hidden sm:block">
                  <p className={`text-sm font-medium ${
                    step >= stepItem.number ? 'text-leaf-600' : 'text-gray-400'
                  }`}>
                    {stepItem.title}
                  </p>
                </div>
                {index < 3 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    step > stepItem.number ? 'bg-leaf-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Error Display */}
        {errors.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4"
          >
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <div>
                <h3 className="text-red-800 font-medium">Please fix the following errors:</h3>
                <ul className="mt-2 text-red-700 text-sm list-disc list-inside">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </motion.div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <motion.div
              key={step}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="card"
            >
              {step === 1 && (
                <div>
                  <h2 className="text-2xl font-bold text-charcoal mb-6 font-heading">
                    Order Details
                  </h2>
                  
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Quantity (kg)
                      </label>
                      <input
                        type="number"
                        min="0.1"
                        max="100"
                        step="0.1"
                        value={orderData.quantity}
                        onChange={(e) => handleInputChange('quantity', parseFloat(e.target.value) || 0)}
                        className="input-field"
                        placeholder="Enter quantity in kg"
                      />
                      <p className="mt-1 text-sm text-gray-500">
                        Minimum order: 0.1 kg, Maximum: 100 kg
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <MapPin className="inline w-4 h-4 mr-1" />
                        Delivery City
                      </label>
                      <select
                        value={orderData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        className="input-field"
                      >
                        <option value="">Select your city</option>
                        {cities.map(city => (
                          <option key={city} value={city}>{city}</option>
                        ))}
                      </select>
                    </div>

                    <div className="flex justify-end">
                      <button
                        onClick={handleNext}
                        disabled={!orderData.city || orderData.quantity <= 0}
                        className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next Step
                        <ArrowRight className="inline w-4 h-4 ml-2" />
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {step === 2 && (
                <div>
                  <h2 className="text-2xl font-bold text-charcoal mb-6 font-heading">
                    Customer Information
                  </h2>
                  
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <User className="inline w-4 h-4 mr-1" />
                        Full Name
                      </label>
                      <input
                        type="text"
                        value={orderData.customerName}
                        onChange={(e) => handleInputChange('customerName', e.target.value)}
                        className="input-field"
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Phone className="inline w-4 h-4 mr-1" />
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={orderData.customerPhone}
                        onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                        className="input-field"
                        placeholder="03XX-XXXXXXX"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Mail className="inline w-4 h-4 mr-1" />
                        Email (Optional)
                      </label>
                      <input
                        type="email"
                        value={orderData.customerEmail}
                        onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                        className="input-field"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Complete Delivery Address
                      </label>
                      <textarea
                        value={orderData.customerAddress}
                        onChange={(e) => handleInputChange('customerAddress', e.target.value)}
                        className="input-field h-24 resize-none"
                        placeholder="House/Flat number, Street, Area, Landmark"
                      />
                    </div>

                    <div className="flex justify-between">
                      <button
                        onClick={() => setStep(1)}
                        className="btn-outline"
                      >
                        Back
                      </button>
                      <button
                        onClick={handleNext}
                        className="btn-primary"
                      >
                        Review Order
                        <ArrowRight className="inline w-4 h-4 ml-2" />
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {step === 3 && (
                <div>
                  <h2 className="text-2xl font-bold text-charcoal mb-6 font-heading">
                    Review Your Order
                  </h2>
                  
                  <div className="space-y-6">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-800 mb-3">Order Summary</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Fresh Mangoes ({orderData.quantity} kg)</span>
                          <span>{pricing ? formatCurrency(pricing.subtotal) : '-'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Delivery to {orderData.city}</span>
                          <span>{pricing ? formatCurrency(pricing.deliveryCharge) : '-'}</span>
                        </div>
                        <div className="border-t pt-2 flex justify-between font-semibold">
                          <span>Total Amount</span>
                          <span>{pricing ? formatCurrency(pricing.total) : '-'}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-800 mb-3">Delivery Details</h3>
                      <div className="text-sm space-y-1">
                        <p><strong>Name:</strong> {orderData.customerName}</p>
                        <p><strong>Phone:</strong> {orderData.customerPhone}</p>
                        {orderData.customerEmail && (
                          <p><strong>Email:</strong> {orderData.customerEmail}</p>
                        )}
                        <p><strong>Address:</strong> {orderData.customerAddress}</p>
                        <p><strong>City:</strong> {orderData.city}</p>
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <button
                        onClick={() => setStep(2)}
                        className="btn-outline"
                      >
                        Back
                      </button>
                      <button
                        onClick={handleSubmitOrder}
                        disabled={loading}
                        className="btn-primary disabled:opacity-50"
                      >
                        {loading ? (
                          <>
                            <div className="spinner mr-2"></div>
                            Processing...
                          </>
                        ) : (
                          <>
                            Confirm Order
                            <CheckCircle className="inline w-4 h-4 ml-2" />
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {step === 4 && orderResult && (
                <div className="text-center">
                  <div className="w-16 h-16 bg-leaf-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle className="w-8 h-8 text-leaf-500" />
                  </div>
                  
                  <h2 className="text-2xl font-bold text-charcoal mb-4 font-heading">
                    Order Confirmed!
                  </h2>
                  
                  <p className="text-gray-600 mb-6">
                    Your order has been successfully placed. Our AI system is now processing it.
                  </p>
                  
                  <div className="bg-leaf-50 rounded-lg p-6 mb-6">
                    <h3 className="font-semibold text-leaf-800 mb-2">Order Details</h3>
                    <p className="text-leaf-700">Order Number: <strong>{orderResult.orderNumber}</strong></p>
                    <p className="text-leaf-700">Total Amount: <strong>{formatCurrency(orderResult.pricing.total)}</strong></p>
                  </div>
                  
                  <div className="space-y-4">
                    <Link href={`/tracking/${orderResult.orderNumber}`} className="btn-primary block">
                      Track Your Order
                    </Link>
                    <Link href="/order" className="btn-outline block">
                      Place Another Order
                    </Link>
                  </div>
                </div>
              )}
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Pricing Card */}
            {step < 4 && pricing && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="card"
              >
                <h3 className="text-lg font-semibold text-charcoal mb-4 font-heading">
                  <Calculator className="inline w-5 h-5 mr-2" />
                  Price Breakdown
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Mangoes ({orderData.quantity} kg)</span>
                    <span>{formatCurrency(pricing.subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Delivery ({orderData.city})</span>
                    <span>{formatCurrency(pricing.deliveryCharge)}</span>
                  </div>
                  <div className="border-t pt-3 flex justify-between font-semibold">
                    <span>Total</span>
                    <span className="text-leaf-600">{formatCurrency(pricing.total)}</span>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Info Card */}
            <div className="card">
              <h3 className="text-lg font-semibold text-charcoal mb-4 font-heading">
                Order Information
              </h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-leaf-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Fresh mangoes directly from farm</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-leaf-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>AI-powered order processing</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-leaf-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Real-time tracking updates</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-leaf-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Secure payment verification</span>
                </div>
              </div>
            </div>

            {/* Support Card */}
            <div className="card">
              <h3 className="text-lg font-semibold text-charcoal mb-4 font-heading">
                Need Help?
              </h3>
              <div className="space-y-3 text-sm">
                <a href={`tel:${config.app.supportPhone}`} className="flex items-center text-leaf-600 hover:text-leaf-700">
                  <Phone className="w-4 h-4 mr-2" />
                  {config.app.supportPhone}
                </a>
                <a href={`mailto:${config.app.supportEmail}`} className="flex items-center text-leaf-600 hover:text-leaf-700">
                  <Mail className="w-4 h-4 mr-2" />
                  {config.app.supportEmail}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
