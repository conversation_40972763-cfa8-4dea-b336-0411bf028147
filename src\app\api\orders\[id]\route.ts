import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Try to find by ID first, then by order number
    let order = await prisma.order.findUnique({
      where: { id },
      include: {
        invoice: true,
        payment: true,
        delivery: true,
        agentLogs: {
          orderBy: { createdAt: 'desc' },
        },
      },
    })

    // If not found by ID, try by order number
    if (!order) {
      order = await prisma.order.findUnique({
        where: { orderNumber: id },
        include: {
          invoice: true,
          payment: true,
          delivery: true,
          agentLogs: {
            orderBy: { createdAt: 'desc' },
          },
        },
      })
    }

    if (!order) {
      return NextResponse.json({
        success: false,
        error: 'Order not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: order
    })

  } catch (error) {
    console.error('Get order error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch order'
    }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()

    // Find the order
    const existingOrder = await prisma.order.findUnique({
      where: { id },
    })

    if (!existingOrder) {
      return NextResponse.json({
        success: false,
        error: 'Order not found'
      }, { status: 404 })
    }

    // Update allowed fields
    const allowedFields = ['status', 'paymentStatus']
    const updateData: any = {}

    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field]
      }
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No valid fields to update'
      }, { status: 400 })
    }

    const updatedOrder = await prisma.order.update({
      where: { id },
      data: updateData,
      include: {
        invoice: true,
        payment: true,
        delivery: true,
        agentLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: updatedOrder
    })

  } catch (error) {
    console.error('Update order error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to update order'
    }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Find the order
    const existingOrder = await prisma.order.findUnique({
      where: { id },
    })

    if (!existingOrder) {
      return NextResponse.json({
        success: false,
        error: 'Order not found'
      }, { status: 404 })
    }

    // Soft delete by updating status to CANCELLED
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: { 
        status: 'CANCELLED',
        updatedAt: new Date(),
      },
    })

    return NextResponse.json({
      success: true,
      data: updatedOrder,
      message: 'Order cancelled successfully'
    })

  } catch (error) {
    console.error('Delete order error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to cancel order'
    }, { status: 500 })
  }
}
