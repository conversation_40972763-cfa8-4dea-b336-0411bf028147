import { prisma } from './db'
import { generateOrderNumber, generateInvoiceNumber } from './utils'
import { OrderStatus, PaymentStatus, PaymentMethod, VerificationStatus, CourierService, DeliveryStatus, AgentType } from '@prisma/client'

export async function seedDemoData() {
  try {
    console.log('🌱 Seeding demo data...')

    // Create demo orders
    const demoOrders = [
      {
        orderNumber: 'MNG-DEMO-001',
        customerName: '<PERSON>',
        customerPhone: '+92-300-1234567',
        customerEmail: '<EMAIL>',
        customerAddress: 'House 123, Block A, Gulshan-e-Iqbal',
        city: 'Karachi',
        quantity: 5.0,
        pricePerKg: 150,
        deliveryCharge: 200,
        totalAmount: 950,
        status: OrderStatus.DELIVERED,
        paymentStatus: PaymentStatus.PAID,
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      },
      {
        orderNumber: 'MNG-DEMO-002',
        customerName: 'Fatima Ali',
        customerPhone: '+92-321-9876543',
        customerEmail: '<EMAIL>',
        customerAddress: 'Flat 45, DHA Phase 5',
        city: 'Lahore',
        quantity: 3.0,
        pricePerKg: 150,
        deliveryCharge: 250,
        totalAmount: 700,
        status: OrderStatus.SHIPPED,
        paymentStatus: PaymentStatus.PAID,
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      },
      {
        orderNumber: 'MNG-DEMO-003',
        customerName: 'Hassan Sheikh',
        customerPhone: '+92-333-5555555',
        customerEmail: '<EMAIL>',
        customerAddress: 'Street 15, F-8/2',
        city: 'Islamabad',
        quantity: 2.5,
        pricePerKg: 150,
        deliveryCharge: 300,
        totalAmount: 675,
        status: OrderStatus.CONFIRMED,
        paymentStatus: PaymentStatus.PENDING,
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      },
      {
        orderNumber: 'MNG-DEMO-004',
        customerName: 'Ayesha Malik',
        customerPhone: '+92-345-7777777',
        customerEmail: '<EMAIL>',
        customerAddress: 'House 67, Satellite Town',
        city: 'Rawalpindi',
        quantity: 4.0,
        pricePerKg: 150,
        deliveryCharge: 300,
        totalAmount: 900,
        status: OrderStatus.PROCESSING,
        paymentStatus: PaymentStatus.PAID,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      }
    ]

    for (const orderData of demoOrders) {
      // Create order
      const order = await prisma.order.create({
        data: orderData
      })

      // Create invoice
      const invoice = await prisma.invoice.create({
        data: {
          orderId: order.id,
          invoiceNumber: generateInvoiceNumber(),
          pdfUrl: `/invoices/${order.orderNumber}.pdf`,
        }
      })

      // Create payment if paid
      if (order.paymentStatus === PaymentStatus.PAID) {
        await prisma.payment.create({
          data: {
            orderId: order.id,
            method: PaymentMethod.JAZZCASH,
            amount: order.totalAmount,
            transactionId: `TXN${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
            verificationStatus: VerificationStatus.VERIFIED,
            verifiedAt: new Date(),
          }
        })
      }

      // Create delivery if shipped or delivered
      if ([OrderStatus.SHIPPED, OrderStatus.DELIVERED].includes(order.status)) {
        await prisma.delivery.create({
          data: {
            orderId: order.id,
            courierService: CourierService.TCS,
            trackingNumber: `TCS${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
            pickupAddress: 'MangoEase Warehouse, Sargodha',
            deliveryAddress: `${order.customerAddress}, ${order.city}`,
            status: order.status === OrderStatus.DELIVERED ? DeliveryStatus.DELIVERED : DeliveryStatus.IN_TRANSIT,
            bookedAt: new Date(order.createdAt.getTime() + 2 * 60 * 60 * 1000), // 2 hours after order
            pickedUpAt: order.status === OrderStatus.DELIVERED ? new Date(order.createdAt.getTime() + 4 * 60 * 60 * 1000) : null,
            deliveredAt: order.status === OrderStatus.DELIVERED ? new Date(order.createdAt.getTime() + 2 * 24 * 60 * 60 * 1000) : null,
          }
        })
      }

      // Create some agent logs
      const agentLogs = [
        {
          orderId: order.id,
          agentType: AgentType.ORDER_INTAKE,
          action: 'order_created',
          input: { customerName: order.customerName, quantity: order.quantity },
          output: { orderId: order.id, orderNumber: order.orderNumber },
          success: true,
        },
        {
          orderId: order.id,
          agentType: AgentType.INVOICE_GENERATOR,
          action: 'invoice_generated',
          input: { orderId: order.id },
          output: { invoiceId: invoice.id, invoiceNumber: invoice.invoiceNumber },
          success: true,
        }
      ]

      if (order.paymentStatus === PaymentStatus.PAID) {
        agentLogs.push({
          orderId: order.id,
          agentType: AgentType.PAYMENT_VERIFIER,
          action: 'payment_verified',
          input: { method: 'JAZZCASH', amount: order.totalAmount },
          output: { verified: true, confidence: 0.95 },
          success: true,
        })
      }

      for (const logData of agentLogs) {
        await prisma.agentLog.create({
          data: {
            ...logData,
            createdAt: new Date(order.createdAt.getTime() + Math.random() * 60 * 60 * 1000), // Random time within 1 hour of order
          }
        })
      }

      console.log(`✅ Created demo order: ${order.orderNumber}`)
    }

    console.log('🎉 Demo data seeded successfully!')
    
    return {
      success: true,
      message: 'Demo data created successfully',
      orders: demoOrders.length
    }

  } catch (error) {
    console.error('❌ Error seeding demo data:', error)
    throw error
  }
}

export async function clearDemoData() {
  try {
    console.log('🧹 Clearing demo data...')

    // Delete in correct order due to foreign key constraints
    await prisma.agentLog.deleteMany({
      where: {
        order: {
          orderNumber: {
            startsWith: 'MNG-DEMO-'
          }
        }
      }
    })

    await prisma.delivery.deleteMany({
      where: {
        order: {
          orderNumber: {
            startsWith: 'MNG-DEMO-'
          }
        }
      }
    })

    await prisma.payment.deleteMany({
      where: {
        order: {
          orderNumber: {
            startsWith: 'MNG-DEMO-'
          }
        }
      }
    })

    await prisma.invoice.deleteMany({
      where: {
        order: {
          orderNumber: {
            startsWith: 'MNG-DEMO-'
          }
        }
      }
    })

    await prisma.order.deleteMany({
      where: {
        orderNumber: {
          startsWith: 'MNG-DEMO-'
        }
      }
    })

    console.log('✅ Demo data cleared successfully!')
    
    return {
      success: true,
      message: 'Demo data cleared successfully'
    }

  } catch (error) {
    console.error('❌ Error clearing demo data:', error)
    throw error
  }
}
