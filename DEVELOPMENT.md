# 🛠️ MangoEase AI - Development Guide

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL or Supabase account
- OpenAI API key

### Quick Setup
```bash
# Clone and setup
git clone <repository-url>
cd mangoease-ai

# Make setup script executable and run
chmod +x setup.sh
./setup.sh

# Or manual setup
npm install
cp .env.example .env
# Edit .env with your configuration
npx prisma generate
npx prisma db push
```

### Environment Variables
Create `.env` file with:
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/mangoease"

# Supabase (alternative)
SUPABASE_URL="your-supabase-url"
SUPABASE_ANON_KEY="your-supabase-anon-key"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"

# Business Settings
MANGO_PRICE_PER_KG=150
DELIVERY_CHARGE_KARACHI=200
DELIVERY_CHARGE_LAHORE=250
DELIVERY_CHARGE_ISLAMABAD=300
DELIVERY_CHARGE_OTHER=350

# Admin
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
```

## 🧪 Development Workflow

### 1. Start Development Server
```bash
npm run dev
```
Visit `http://localhost:3000`

### 2. Database Operations
```bash
# Push schema changes
npx prisma db push

# View database
npx prisma studio

# Generate client after schema changes
npx prisma generate
```

### 3. Seed Demo Data
```bash
# Via API (recommended)
curl -X POST http://localhost:3000/api/admin/seed-demo \
  -H "Content-Type: application/json" \
  -d '{"action": "seed"}'

# Clear demo data
curl -X POST http://localhost:3000/api/admin/seed-demo \
  -H "Content-Type: application/json" \
  -d '{"action": "clear"}'
```

## 🧩 Architecture Overview

### Frontend Structure
```
src/app/
├── page.tsx              # Landing page
├── order/                # Customer order flow
├── tracking/             # Order tracking
├── (dashboard)/admin/    # Admin dashboard
└── api/                  # API routes
```

### AI Agents
```
src/agents/
├── base-agent.ts         # Base agent class
├── order-intake-agent.ts # Process orders
├── invoice-agent.ts      # Generate invoices
├── payment-verification-agent.ts # Verify payments
└── delivery-agent.ts     # Handle delivery
```

### Key Components
```
src/components/
├── ui/                   # Reusable UI components
├── forms/                # Form components
└── layout/               # Layout components
```

## 🔧 Customization

### Business Logic
Edit `src/lib/config.ts`:
```typescript
export const config = {
  pricing: {
    pricePerKg: 150,
    deliveryCharges: {
      'Karachi': 200,
      'Lahore': 250,
      // Add more cities
    }
  }
}
```

### Styling
- Colors: `tailwind.config.js`
- Components: `src/app/globals.css`
- Theme: Mango yellow (#FFC107) + Leaf green (#4CAF50)

### AI Agents
Extend `BaseAgent` class:
```typescript
export class CustomAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super(AgentType.CUSTOM, context)
  }

  async execute(input: any): Promise<AgentResult> {
    // Your logic here
  }
}
```

## 🧪 Testing

### Manual Testing
1. **Order Flow**: `/order` → Fill form → Submit
2. **Tracking**: `/tracking` → Enter `MNG-DEMO-001`
3. **Admin**: `/admin` → View dashboard

### API Testing
```bash
# Create order
curl -X POST http://localhost:3000/api/orders \
  -H "Content-Type: application/json" \
  -d '{
    "customerName": "Test User",
    "customerPhone": "03001234567",
    "customerAddress": "Test Address",
    "city": "Karachi",
    "quantity": 2
  }'

# Get order
curl http://localhost:3000/api/orders/ORDER_ID

# Admin stats
curl http://localhost:3000/api/admin/stats
```

## 🚀 Deployment

### Vercel (Recommended)
1. Push to GitHub
2. Connect to Vercel
3. Set environment variables
4. Deploy

### Railway
1. Connect GitHub repo
2. Add PostgreSQL service
3. Set environment variables
4. Deploy

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   # Check DATABASE_URL format
   postgresql://username:password@host:port/database
   ```

2. **Prisma Client Error**
   ```bash
   npx prisma generate
   ```

3. **OpenAI API Error**
   ```bash
   # Check OPENAI_API_KEY in .env
   ```

4. **Build Errors**
   ```bash
   # Clear Next.js cache
   rm -rf .next
   npm run build
   ```

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev

# Or specific modules
DEBUG=prisma:* npm run dev
```

## 📊 Monitoring

### Agent Logs
View in admin dashboard or database:
```sql
SELECT * FROM agent_logs 
ORDER BY created_at DESC 
LIMIT 10;
```

### Performance
- Use Next.js built-in analytics
- Monitor API response times
- Track agent execution times

## 🔄 Updates

### Schema Changes
```bash
# 1. Update prisma/schema.prisma
# 2. Push changes
npx prisma db push
# 3. Generate client
npx prisma generate
```

### Dependencies
```bash
# Update all
npm update

# Update specific package
npm install package@latest
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Make changes
4. Test thoroughly
5. Commit: `git commit -m 'Add amazing feature'`
6. Push: `git push origin feature/amazing-feature`
7. Create Pull Request

### Code Style
- Use TypeScript
- Follow ESLint rules
- Use Prettier for formatting
- Write meaningful commit messages

### Testing Checklist
- [ ] Order creation works
- [ ] Payment verification works
- [ ] Tracking displays correctly
- [ ] Admin dashboard loads
- [ ] Mobile responsive
- [ ] No console errors

## 📚 Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [OpenAI API](https://platform.openai.com/docs)
- [Framer Motion](https://www.framer.com/motion/)

## 🆘 Support

- **Issues**: Create GitHub issue
- **Questions**: Discussion board
- **Email**: <EMAIL>

---

Happy coding! 🥭✨
