export const config = {
  // Business Settings
  pricing: {
    pricePerKg: Number(process.env.MANGO_PRICE_PER_KG) || 150,
    deliveryCharges: {
      'Karachi': Number(process.env.DELIVERY_CHARGE_KARACHI) || 200,
      'Lahore': Number(process.env.DELIVERY_CHARGE_LAHORE) || 250,
      'Islamabad': Number(process.env.DELIVERY_CHARGE_ISLAMABAD) || 300,
      'Rawalpindi': Number(process.env.DELIVERY_CHARGE_ISLAMABAD) || 300,
      'Faisalabad': 280,
      'Multan': 300,
      'Peshawar': 350,
      'Quetta': 400,
      'Other': Number(process.env.DELIVERY_CHARGE_OTHER) || 350,
    }
  },

  // API Keys
  openai: {
    apiKey: process.env.OPENAI_API_KEY!,
  },

  supabase: {
    url: process.env.SUPABASE_URL!,
    anonKey: process.env.SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  },

  // Courier Services
  courier: {
    tcs: {
      apiKey: process.env.TCS_API_KEY,
      baseUrl: 'https://api.tcs.com.pk',
    },
    leopards: {
      apiKey: process.env.LEOPARDS_API_KEY,
      baseUrl: 'https://api.leopardscourier.com',
    }
  },

  // Notifications
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID,
    authToken: process.env.TWILIO_AUTH_TOKEN,
    phoneNumber: process.env.TWILIO_PHONE_NUMBER,
  },

  smtp: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: Number(process.env.SMTP_PORT) || 587,
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },

  // Admin
  admin: {
    email: process.env.ADMIN_EMAIL || '<EMAIL>',
    password: process.env.ADMIN_PASSWORD || 'admin123',
  },

  // App Settings
  app: {
    name: 'MangoEase AI',
    tagline: 'Harvest to Home, Hassle-Free',
    supportPhone: '+92-300-1234567',
    supportEmail: '<EMAIL>',
  }
}

export const getCityDeliveryCharge = (city: string): number => {
  const normalizedCity = city.trim()
  return config.pricing.deliveryCharges[normalizedCity] || config.pricing.deliveryCharges['Other']
}

export const calculateOrderTotal = (quantity: number, city: string): {
  subtotal: number
  deliveryCharge: number
  total: number
} => {
  const subtotal = quantity * config.pricing.pricePerKg
  const deliveryCharge = getCityDeliveryCharge(city)
  const total = subtotal + deliveryCharge

  return {
    subtotal,
    deliveryCharge,
    total
  }
}
